package goupload

import (
	"errors"
	"fmt"
	"math"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	gonanoid "github.com/matoous/go-nanoid/v2"
)

// GenerateFilename 生成唯一的文件名，保留原始文件扩展名
// 如果无法生成唯一的ID，它将返回一个错误。
func GenerateFilename(originalFilename string) (string, error) {
	// 提取文件扩展名
	ext := filepath.Ext(originalFilename)

	// 生成nanoid (默认长度21)
	id, err := gonanoid.New()
	if err != nil {
		// 如果nanoid生成失败，这是一个严重的系统问题，我们无法保证唯一性。
		// 返回错误而不是使用一个不可靠的后备方案。
		return "", fmt.Errorf("failed to generate unique id: %w", err)
	}

	// 如果有扩展名，则添加；否则不添加
	if ext != "" {
		return id + ext, nil
	}
	return id, nil
}

// ValidateFilename 验证文件名是否合法
func ValidateFilename(filename string) error {
	if filename == "" {
		return fmt.Errorf("filename cannot be empty")
	}

	// 检查文件名长度
	if len(filename) > 255 {
		return fmt.Errorf("filename length cannot exceed 255 characters")
	}

	// 检查是否包含非法字符
	illegalChars := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
	for _, char := range illegalChars {
		if strings.Contains(filename, char) {
			return fmt.Errorf("filename cannot contain illegal character: %s", char)
		}
	}

	// 检查是否为保留名称 (Windows)
	reservedNames := []string{
		"CON", "PRN", "AUX", "NUL",
		"COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
		"LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9",
	}

	nameWithoutExt := strings.TrimSuffix(filename, filepath.Ext(filename))
	upperName := strings.ToUpper(nameWithoutExt)

	for _, reserved := range reservedNames {
		if upperName == reserved {
			return fmt.Errorf("filename cannot be a reserved name: %s", reserved)
		}
	}

	return nil
}

// ExecuteWithRetry 执行带重试的操作
func ExecuteWithRetry(operation func() error, maxRetries int, delay time.Duration) error {
	var lastErr error

	for i := 0; i <= maxRetries; i++ {
		err := operation()
		if err == nil {
			return nil
		}

		lastErr = err

		// 如果还有重试机会，等待后重试
		if i < maxRetries {
			time.Sleep(delay)
		}
	}

	return lastErr
}

// GeneratePathSeed 生成路径计算的种子字符串
func GeneratePathSeed(filename string, timestamp int64, uid string) string {
	return fmt.Sprintf("%s_%d_%s", filename, timestamp, uid)
}

// It is case-insensitive and supports K, M, G, T, P suffixes.
func parseSize(sizeStr string) (int64, error) {
	sizeStr = strings.TrimSpace(sizeStr)
	if sizeStr == "" {
		return 0, errors.New("empty size string")
	}

	// Find the first letter to split number and unit
	firstLetterIdx := -1
	for i, r := range sizeStr {
		if r >= 'A' && r <= 'Z' || r >= 'a' && r <= 'z' {
			firstLetterIdx = i
			break
		}
	}

	var numPart, unitPart string
	if firstLetterIdx == -1 {
		numPart = sizeStr
		unitPart = ""
	} else {
		numPart = strings.TrimSpace(sizeStr[:firstLetterIdx])
		unitPart = strings.ToUpper(strings.TrimSpace(sizeStr[firstLetterIdx:]))
	}

	value, err := strconv.ParseFloat(numPart, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid number for size: %q", numPart)
	}

	var multiplier int64 = 1
	if unitPart != "" {
		// Unconditionally trim "B" suffix, as TrimSuffix handles the check internally.
		unitPart = strings.TrimSuffix(unitPart, "B")

		switch unitPart {
		case "K":
			multiplier = 1024
		case "M":
			multiplier = 1024 * 1024
		case "G":
			multiplier = 1024 * 1024 * 1024
		case "T":
			multiplier = 1024 * 1024 * 1024 * 1024
		case "P":
			multiplier = 1024 * 1024 * 1024 * 1024 * 1024
		case "": // This can happen for inputs like "123B"
			multiplier = 1
		default:
			return 0, fmt.Errorf("invalid unit for size: %q", unitPart)
		}
	}

	if value < 0 {
		return 0, fmt.Errorf("size cannot be negative: %f", value)
	}

	// Pre-flight overflow check
	if value > 0 && multiplier > 0 && value > float64(math.MaxInt64)/float64(multiplier) {
		return 0, fmt.Errorf("size value out of range: %s%s", numPart, unitPart)
	}

	return int64(value * float64(multiplier)), nil
}
