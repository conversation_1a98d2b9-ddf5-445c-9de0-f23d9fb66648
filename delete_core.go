package goupload

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	levelStore "github.com/real-rm/golevelstore"
)

// DeleteResult 表示删除操作的结果
type DeleteResult struct {
	Path            string        `json:"path"`
	DeletedPaths    []DeletedPath `json:"deleted_paths"`
	FailedPaths     []FailedPath  `json:"failed_paths,omitempty"`
	IsPartialDelete bool          `json:"is_partial_delete"`
}

// DeletedPath 表示已删除的路径信息
type DeletedPath struct {
	Type     string `json:"type"` // "local" 或 "s3"
	Path     string `json:"path"`
	Target   string `json:"target,omitempty"`   // S3目标名称
	Bucket   string `json:"bucket,omitempty"`   // S3 bucket
	Endpoint string `json:"endpoint,omitempty"` // S3端点
}

// FailedPath 表示删除失败的路径信息
type FailedPath struct {
	Type    string `json:"type"`
	Path    string `json:"path"`
	Target  string `json:"target,omitempty"`
	Message string `json:"message"`
}

// Delete 高级API函数，提供完整的文件删除流程
func Delete(ctx context.Context, statsUpdater StatsUpdater, site, entryName, relativePath string) (*DeleteResult, error) {
	// 1. 获取用户配置
	config, err := levelStore.GetUserUploadConfig(site, entryName, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get config: %w", err)
	}

	// 2. 获取S3连接配置(如果需要)
	s3ProviderMap, err := getS3ProviderMap(ctx, config.Storage)
	if err != nil {
		return nil, err // 错误已包装
	}

	// 3. 准备S3客户端
	s3ClientMap := make(map[string]s3API)
	for name, p := range s3ProviderMap {
		client, err := getOrNewS3Client(ctx, p)
		if err != nil {
			return nil, fmt.Errorf("failed to initialize s3 client for provider '%s': %w", name, err)
		}
		s3ClientMap[name] = client
	}

	// 4. 执行多目标删除
	writeOpts := extractWriteOptions() // 复用上传时的选项
	result := &DeleteResult{
		Path: relativePath,
	}

	var wg sync.WaitGroup
	var mu sync.Mutex // 保护结果并发更新

	for _, storage := range config.Storage {
		wg.Add(1)
		go func(storage levelStore.StorageConfig) {
			defer wg.Done()
			var err error

			switch storage.Type {
			case "local":
				fullPath := filepath.Join(storage.Path, relativePath)
				err = deleteLocalFile(fullPath)
				if err == nil {
					mu.Lock()
					result.DeletedPaths = append(result.DeletedPaths, DeletedPath{
						Type: "local",
						Path: fullPath,
					})
					mu.Unlock()
				} else if !os.IsNotExist(err) {
					mu.Lock()
					result.FailedPaths = append(result.FailedPaths, FailedPath{
						Type:    "local",
						Path:    fullPath,
						Message: err.Error(),
					})
					mu.Unlock()
				}

			case "s3":
				client, ok := s3ClientMap[storage.Target]
				if !ok {
					mu.Lock()
					result.FailedPaths = append(result.FailedPaths, FailedPath{
						Type:    "s3",
						Path:    relativePath,
						Target:  storage.Target,
						Message: "s3 client not found",
					})
					mu.Unlock()
					return
				}

				provider := s3ProviderMap[storage.Target]
				err = deleteS3Object(ctx, client, storage.Bucket, relativePath, writeOpts)
				if err == nil {
					mu.Lock()
					result.DeletedPaths = append(result.DeletedPaths, DeletedPath{
						Type:     "s3",
						Path:     relativePath,
						Target:   storage.Target,
						Bucket:   storage.Bucket,
						Endpoint: provider.Endpoint,
					})
					mu.Unlock()
				} else {
					mu.Lock()
					result.FailedPaths = append(result.FailedPaths, FailedPath{
						Type:    "s3",
						Path:    relativePath,
						Target:  storage.Target,
						Message: err.Error(),
					})
					mu.Unlock()
				}
			}
		}(storage)
	}

	wg.Wait()

	// 5. 更新删除状态
	if len(result.FailedPaths) > 0 {
		result.IsPartialDelete = len(result.DeletedPaths) > 0
		if len(result.DeletedPaths) == 0 {
			// 完全失败
			return result, fmt.Errorf("all delete operations failed")
		}
	}

	// 6. 更新统计信息
	if statsUpdater != nil && len(result.DeletedPaths) > 0 {
		// 从相对路径中提取L1和L2
		parts := strings.Split(relativePath, "/")
		if len(parts) >= 2 {
			l1 := parts[0]
			l2 := parts[1]
			statsUpdater.AddDirStats(l1, l2, -1, -1) // 减少一个实体和一个文件
		}
	}

	return result, nil
}

// 辅助函数: 删除本地文件或目录
func deleteLocalFile(fullPath string) error {
	return deleteLocalPath(fullPath)
}

// deleteLocalPath 删除本地文件或目录（支持递归删除）
func deleteLocalPath(fullPath string) error {
	info, err := os.Stat(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // 文件不存在，认为删除成功
		}
		return fmt.Errorf("failed to stat path %s: %w", fullPath, err)
	}

	if info.IsDir() {
		// 删除目录及其所有内容
		err = os.RemoveAll(fullPath)
		if err != nil {
			return fmt.Errorf("failed to remove directory %s: %w", fullPath, err)
		}
	} else {
		// 删除单个文件
		err = os.Remove(fullPath)
		if err != nil {
			return fmt.Errorf("failed to remove file %s: %w", fullPath, err)
		}
	}
	return nil
}
