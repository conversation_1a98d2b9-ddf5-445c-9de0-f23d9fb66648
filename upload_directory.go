package goupload

import (
	"context"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	levelStore "github.com/real-rm/golevelstore"
	"github.com/real-rm/golog"
)

// DirectoryUploadRequest 目录上传请求
type DirectoryUploadRequest struct {
	Site          string               // 板块名称
	EntryName     string               // 入口类型
	UserID        string               // 用户ID
	DirectoryName string               // 目录名称
	Files         []DirectoryFileEntry // 文件列表
	MaxSize       int64                // 目录总大小限制（可选，0表示使用配置默认值）
}

// DirectoryFileEntry 目录文件条目
type DirectoryFileEntry struct {
	RelativePath string     // 文件在目录中的相对路径，如 "subdir/file.txt"
	Reader       io.Reader  // 文件内容
	Size         int64      // 文件大小
	ModTime      *time.Time // 文件修改时间（可选）
}

// DirectoryUploadResult 目录上传结果
type DirectoryUploadResult struct {
	DirectoryPath   string               // 目录在存储中的路径
	TotalFiles      int                  // 总文件数
	TotalSize       int64                // 总大小
	SuccessFiles    []string             // 成功上传的文件相对路径
	FailedFiles     []DirectoryFileError // 失败的文件
	WrittenPaths    []WrittenPath        // 所有写入的存储路径
	IsPartialUpload bool                 // 是否部分上传成功
}

// DirectoryFileError 文件上传错误
type DirectoryFileError struct {
	RelativePath string // 文件相对路径
	Error        string // 错误信息
}

// ProcessedDirectoryFile 预处理后的目录文件
type ProcessedDirectoryFile struct {
	RelativePath string
	Reader       io.ReadSeeker // 统一转换为可重复读取
	FileInfo     *FileInfo
	ModTime      *time.Time
	CleanupFunc  func() // 清理函数（如果需要）
}

// UploadDirectory 上传目录
func UploadDirectory(
	ctx context.Context,
	statsUpdater StatsUpdater,
	request *DirectoryUploadRequest,
) (*DirectoryUploadResult, error) {
	// 1. 验证请求
	if err := validateDirectoryRequest(request); err != nil {
		return nil, err
	}

	// 2. 获取配置（复用现有逻辑）
	provider := &LevelStoreProvider{}
	prereqs, err := prepareUploadPrerequisites(
		provider,
		request.Site,
		request.EntryName,
		request.UserID,
		request.DirectoryName,
	)
	if err != nil {
		return nil, err
	}

	// 3. 确定目录大小限制
	effectiveLimit := determineDirectoryLimit(prereqs.Config.MaxSize, request.MaxSize)

	// 4. 预处理所有文件 - 关键步骤！
	processedFiles, totalSize, cleanup, err := preprocessDirectoryFiles(
		request.Files,
		effectiveLimit,
		prereqs.Config.TmpPath,
	)
	if cleanup != nil {
		defer cleanup()
	}
	if err != nil {
		return nil, err
	}

	// 5. 获取S3配置
	s3ProviderMap, err := getS3ProviderMap(ctx, prereqs.Config.Storage)
	if err != nil {
		return nil, err
	}

	// 6. 执行目录上传
	result, err := executeDirectoryUpload(
		ctx,
		prereqs.Config,
		s3ProviderMap,
		processedFiles,
		prereqs.RelativePath,
		prereqs.Metadata,
	)
	if err != nil {
		return nil, err
	}

	// 7. 更新统计
	if result.TotalFiles > 0 && statsUpdater != nil {
		parts := strings.Split(prereqs.RelativePath, "/")
		if len(parts) >= 2 {
			l1, l2 := parts[0], parts[1]
			// 目录算1个实体，文件数为实际文件数
			statsUpdater.AddDirStats(l1, l2, 1, result.TotalFiles)
		} else {
			golog.Warn("Failed to update directory statistics due to invalid path",
				"site", request.Site, "path", prereqs.RelativePath)
		}
	} else {
		golog.Warn("StatsUpdater is nil, skipping statistics update", "site", request.Site)
	}

	result.DirectoryPath = prereqs.RelativePath
	result.TotalSize = totalSize

	return result, nil
}

// validateDirectoryRequest 验证目录上传请求
func validateDirectoryRequest(request *DirectoryUploadRequest) error {
	if request == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if request.Site == "" {
		return fmt.Errorf("site cannot be empty")
	}
	if request.EntryName == "" {
		return fmt.Errorf("entryName cannot be empty")
	}
	if request.UserID == "" {
		return fmt.Errorf("userID cannot be empty")
	}
	if request.DirectoryName == "" {
		return fmt.Errorf("directoryName cannot be empty")
	}
	if len(request.Files) == 0 {
		return fmt.Errorf("files list cannot be empty")
	}

	// 验证文件路径
	for i, file := range request.Files {
		if file.RelativePath == "" {
			return fmt.Errorf("file %d: relativePath cannot be empty", i)
		}
		if file.Reader == nil {
			return fmt.Errorf("file %d: reader cannot be nil", i)
		}
		// 检查路径安全性
		if err := validateRelativePath(file.RelativePath); err != nil {
			return fmt.Errorf("file %d: %w", i, err)
		}
	}

	return nil
}

// validateRelativePath 验证相对路径的安全性
func validateRelativePath(path string) error {
	// 检查空路径
	if path == "" {
		return fmt.Errorf("path cannot be empty")
	}

	// 检查原始路径中的路径遍历
	if strings.Contains(path, "..") {
		return fmt.Errorf("path traversal not allowed: %s", path)
	}

	// 清理路径
	cleanPath := filepath.Clean(path)

	// 再次检查清理后的路径遍历
	if strings.Contains(cleanPath, "..") {
		return fmt.Errorf("path traversal not allowed: %s", path)
	}

	// 检查绝对路径（包括Windows路径）
	if filepath.IsAbs(cleanPath) {
		return fmt.Errorf("absolute path not allowed: %s", path)
	}

	// 检查Windows绝对路径（如 C:\Windows\System32）
	if len(cleanPath) >= 3 && cleanPath[1] == ':' && (cleanPath[2] == '\\' || cleanPath[2] == '/') {
		return fmt.Errorf("absolute path not allowed: %s", path)
	}

	// 检查路径长度
	if len(cleanPath) > 500 {
		return fmt.Errorf("path too long (max 500 chars): %s", path)
	}

	// 检查路径是否试图跳出当前目录
	if strings.HasPrefix(cleanPath, "../") || cleanPath == ".." {
		return fmt.Errorf("path traversal not allowed: %s", path)
	}

	return nil
}

// determineDirectoryLimit 确定目录大小限制
func determineDirectoryLimit(configMaxSize string, requestMaxSize int64) int64 {
	// 如果请求中指定了限制，使用请求的限制
	if requestMaxSize > 0 {
		return requestMaxSize
	}

	// 否则使用配置中的限制
	if configMaxSize != "" {
		if size, err := parseSize(configMaxSize); err == nil {
			return size
		}
	}

	// 默认限制：1GB
	return 1024 * 1024 * 1024
}

// preprocessDirectoryFiles 预处理目录中的所有文件
// 这是核心逻辑：区分seeker和non-seeker，统一处理
func preprocessDirectoryFiles(
	files []DirectoryFileEntry,
	maxTotalSize int64,
	tempDir string,
) ([]ProcessedDirectoryFile, int64, func(), error) {

	var processedFiles []ProcessedDirectoryFile
	var cleanupFuncs []func()
	var totalSize int64

	// 统一清理函数
	cleanup := func() {
		for _, fn := range cleanupFuncs {
			if fn != nil {
				fn()
			}
		}
	}

	for _, file := range files {
		// 复用现有的 getFileInfoWithRewindableReader 逻辑！
		rewindableReader, fileInfo, fileCleanup, err := getFileInfoWithRewindableReader(
			file.Reader,
			filepath.Base(file.RelativePath),
			maxTotalSize, // 单个文件也不能超过总限制
			tempDir,
		)

		if err != nil {
			cleanup()
			return nil, 0, nil, fmt.Errorf("failed to process file %s: %w", file.RelativePath, err)
		}

		if fileCleanup != nil {
			cleanupFuncs = append(cleanupFuncs, fileCleanup)
		}

		// 累计大小检查
		totalSize += fileInfo.Size
		if totalSize > maxTotalSize {
			cleanup()
			return nil, 0, nil, fmt.Errorf("directory total size %d exceeds limit %d", totalSize, maxTotalSize)
		}

		processedFiles = append(processedFiles, ProcessedDirectoryFile{
			RelativePath: file.RelativePath,
			Reader:       rewindableReader,
			FileInfo:     fileInfo,
			ModTime:      file.ModTime,
			CleanupFunc:  fileCleanup,
		})
	}

	return processedFiles, totalSize, cleanup, nil
}

// executeDirectoryUpload 执行目录上传到所有存储位置
func executeDirectoryUpload(
	ctx context.Context,
	config *levelStore.UserUploadConfig,
	s3ProviderMap map[string]levelStore.S3ProviderConfig,
	files []ProcessedDirectoryFile,
	basePath string,
	metadata map[string]string,
) (*DirectoryUploadResult, error) {

	result := &DirectoryUploadResult{
		TotalFiles: len(files),
	}

	writeOpts := extractWriteOptions()

	// 为每个文件执行上传
	for _, file := range files {
		// 构建文件的完整相对路径
		fileRelativePath := filepath.Join(basePath, file.RelativePath)
		// 统一使用正斜杠
		fileRelativePath = filepath.ToSlash(fileRelativePath)

		// 使用现有的WriteToLocationsWithRollback函数
		writtenPaths, err := WriteToLocationsWithRollback(
			ctx,
			config,
			s3ProviderMap,
			file.Reader,
			fileRelativePath,
			writeOpts,
			metadata,
			file.FileInfo,
		)

		if err != nil {
			// 记录失败的文件
			result.FailedFiles = append(result.FailedFiles, DirectoryFileError{
				RelativePath: file.RelativePath,
				Error:        err.Error(),
			})
			result.IsPartialUpload = true
		} else {
			// 记录成功的文件
			result.SuccessFiles = append(result.SuccessFiles, file.RelativePath)
			result.WrittenPaths = append(result.WrittenPaths, writtenPaths...)
			// 累计成功上传文件的大小
			result.TotalSize += file.FileInfo.Size
		}
	}

	// 如果所有文件都失败了，返回错误
	if len(result.SuccessFiles) == 0 {
		return nil, fmt.Errorf("all files failed to upload")
	}

	return result, nil
}
