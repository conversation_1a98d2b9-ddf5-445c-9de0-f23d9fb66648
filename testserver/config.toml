# Test server configuration for goupload
# This file provides configuration for the test server

[server]
port = 8080
host = "localhost"

[golog]
dir    = "./logs"  # 日志文件存放目录
level  = "info"    # 日志级别 (debug, info, warn, error)
format = "text"    # 日志格式 (text or json)

# Global write options
[write_options]
max_retries = 3
retry_delay = "1s"
s3_timeout = "30s"
chunk_size = 5242880 # 5MB in bytes
enable_logging = true
validate_content = true
enable_metadata = true

# S3 providers (using local GarageHQ for testing)
[connection_sources]

  [[connection_sources.s3_providers]]
    name = "garage-primary"
    endpoint = "http://localhost:3900"
    key = "GK4ff5d815be8b4c55a3ad613d"  # 从 get-garage-keys.sh 获取实际密钥
    pass = "bc1ab20e40a656d52951a84c946139f9dd466825c4a680d8f541db86c29fa035"
    region = "garage"

# User upload configuration
[userupload]
site = "TEST"

  # 单文件上传配置
  [[userupload.types]]
    entryName = "test_upload"
    prefix = "/uploads"
    tmpPath = "./temp/uploads"
    maxSize = "500MB"  # 支持单文件上传最大限制
    storage = [
      { type = "local", path = "uploads/files" },
      { type = "s3", target = "garage-primary", bucket = "test-bucket" }
    ]

  # 分块上传配置
  [[userupload.types]]
    entryName = "chunked_upload"
    prefix = "/chunked"
    tmpPath = "./temp/chunked"
    maxSize = "10GB"   # 支持分块上传最大限制
    storage = [
      { type = "local", path = "uploads/chunked" },
      { type = "s3", target = "garage-primary", bucket = "test-multi-bucket" }
    ]




# 数据库配置（用于DirKeyStore统计功能）
[dbs]
verbose = 3

[dbs.tmp]
uri = "mongodb://127.0.0.1:27017/goupload_test"  # 连接到本地MongoDB


