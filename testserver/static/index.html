<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoUpload 测试服务器</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 GoUpload 测试服务器</h1>
            <p>测试 goupload 库的单文件上传和分块上传功能</p>
        </header>

        <!-- 系统状态监控 -->
        <div class="system-status">
            <h2>🔧 系统状态</h2>
            <div class="status-grid">
                <div class="status-card" id="mongodbStatus">
                    <h3>🗄️ MongoDB</h3>
                    <div class="status-indicator" id="mongodbIndicator">
                        <span class="status-dot checking"></span>
                        <span class="status-text">检查中...</span>
                    </div>
                    <div class="status-details" id="mongodbDetails"></div>
                </div>
                <div class="status-card" id="dirstoreStatus">
                    <h3>📊 DirKeyStore</h3>
                    <div class="status-indicator" id="dirstoreIndicator">
                        <span class="status-dot checking"></span>
                        <span class="status-text">检查中...</span>
                    </div>
                    <div class="status-details" id="dirstoreDetails"></div>
                </div>
                <div class="status-card" id="statsViewStatus">
                    <h3>📈 统计数据</h3>
                    <div class="stats-controls">
                        <select id="statsL1Select" style="display:none;"></select>
                        <button id="loadL1Btn" class="load-btn">加载L1目录</button>
                        <button id="viewStatsBtn" class="view-stats-btn" style="display:none;">查看详情</button>
                    </div>
                    <div class="status-details" id="statsDetails"></div>
                </div>
            </div>
            <button id="refreshStatusBtn" class="refresh-btn">🔄 刷新状态</button>
        </div>

        <main>
            <!-- 上传模式选择 -->
            <div class="mode-selector">
                <h2>📋 选择上传模式</h2>
                <div class="mode-buttons">
                    <button id="singleModeBtn" class="mode-btn active">📤 单文件上传</button>
                    <button id="chunkedModeBtn" class="mode-btn">📦 分块上传</button>
                </div>
            </div>

            <!-- 单文件上传区域 -->
            <div id="singleUploadSection" class="upload-section">
                <h2>📤 单文件上传</h2>
                <p class="description">适用于小于500MB的文件，简单快速</p>

                <form id="singleUploadForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="singleFile">选择文件:</label>
                        <input type="file" id="singleFile" name="file" required>
                        <small>最大支持 500MB</small>
                    </div>

                    <div class="form-group">
                        <label for="singleUid">用户ID (可选):</label>
                        <input type="text" id="singleUid" name="uid" placeholder="anonymous">
                    </div>

                    <button type="submit" id="singleUploadBtn">开始上传</button>
                </form>

                <div id="singleProgress" class="progress-container" style="display: none;">
                    <div class="progress-bar">
                        <div id="singleProgressBar" class="progress-fill"></div>
                    </div>
                    <div id="singleProgressText">0%</div>
                </div>

                <div id="singleResult" class="result-container"></div>
            </div>

            <!-- 分块上传区域 -->
            <div id="chunkedUploadSection" class="upload-section" style="display: none;">
                <h2>📦 分块上传</h2>
                <p class="description">适用于大文件（最大10GB），支持断点续传</p>

                <form id="chunkedUploadForm">
                    <div class="form-group">
                        <label for="chunkedFile">选择文件:</label>
                        <input type="file" id="chunkedFile" name="file" required>
                        <small>最大支持 10GB</small>
                    </div>

                    <div class="form-group">
                        <label for="chunkedUid">用户ID (可选):</label>
                        <input type="text" id="chunkedUid" name="uid" placeholder="anonymous">
                    </div>

                    <div class="form-group">
                        <label for="chunkSize">分块大小:</label>
                        <select id="chunkSize">
                            <option value="1048576">1MB</option>
                            <option value="5242880" selected>5MB</option>
                            <option value="10485760">10MB</option>
                            <option value="20971520">20MB</option>
                        </select>
                    </div>

                    <button type="submit" id="chunkedUploadBtn">开始分块上传</button>
                    <button type="button" id="abortBtn" style="display: none;" class="abort-btn">中止上传</button>
                </form>

                <div id="chunkedProgress" class="progress-container" style="display: none;">
                    <div class="progress-info">
                        <span id="chunkedProgressText">准备中...</span>
                        <span id="chunkedSpeedText"></span>
                    </div>
                    <div class="progress-bar">
                        <div id="chunkedProgressBar" class="progress-fill"></div>
                    </div>
                    <div class="chunk-info">
                        <span id="chunkInfo">分块: 0/0</span>
                        <span id="uploadedSize">已上传: 0 MB</span>
                    </div>
                </div>

                <div id="chunkedResult" class="result-container"></div>
            </div>

            <!-- 日志区域 -->
            <div class="log-section">
                <h2>📝 操作日志</h2>
                <div id="logContent"></div>
                <button id="clearLogBtn" class="clear-btn">清空日志</button>
            </div>
        </main>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
