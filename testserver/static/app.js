// GoUpload 测试服务器前端脚本

// 全局变量
let currentUploadID = null;
let isUploading = false;
let uploadStartTime = null;
let shouldAbortUpload = false; // 添加中止标志

// DOM 元素
const singleModeBtn = document.getElementById('singleModeBtn');
const chunkedModeBtn = document.getElementById('chunkedModeBtn');
const singleUploadSection = document.getElementById('singleUploadSection');
const chunkedUploadSection = document.getElementById('chunkedUploadSection');
const logContent = document.getElementById('logContent');
const clearLogBtn = document.getElementById('clearLogBtn');

// 系统状态监控元素
const mongodbIndicator = document.getElementById('mongodbIndicator');
const mongodbDetails = document.getElementById('mongodbDetails');
const dirstoreIndicator = document.getElementById('dirstoreIndicator');
const dirstoreDetails = document.getElementById('dirstoreDetails');
const refreshStatusBtn = document.getElementById('refreshStatusBtn');

// 统计数据查看元素
const loadL1Btn = document.getElementById('loadL1Btn');
const statsL1Select = document.getElementById('statsL1Select');
const viewStatsBtn = document.getElementById('viewStatsBtn');
const statsDetails = document.getElementById('statsDetails');

// 单文件上传元素
const singleUploadForm = document.getElementById('singleUploadForm');
const singleFile = document.getElementById('singleFile');
const singleUid = document.getElementById('singleUid');
const singleUploadBtn = document.getElementById('singleUploadBtn');
const singleProgress = document.getElementById('singleProgress');
const singleProgressBar = document.getElementById('singleProgressBar');
const singleProgressText = document.getElementById('singleProgressText');
const singleResult = document.getElementById('singleResult');

// 分块上传元素
const chunkedUploadForm = document.getElementById('chunkedUploadForm');
const chunkedFile = document.getElementById('chunkedFile');
const chunkedUid = document.getElementById('chunkedUid');
const chunkSize = document.getElementById('chunkSize');
const chunkedUploadBtn = document.getElementById('chunkedUploadBtn');
const abortBtn = document.getElementById('abortBtn');
const chunkedProgress = document.getElementById('chunkedProgress');
const chunkedProgressBar = document.getElementById('chunkedProgressBar');
const chunkedProgressText = document.getElementById('chunkedProgressText');
const chunkedSpeedText = document.getElementById('chunkedSpeedText');
const chunkInfo = document.getElementById('chunkInfo');
const uploadedSize = document.getElementById('uploadedSize');
const chunkedResult = document.getElementById('chunkedResult');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    addLog('🎉 GoUpload 测试服务器已启动', 'success');
    
    // 模式切换事件
    singleModeBtn.addEventListener('click', () => switchMode('single'));
    chunkedModeBtn.addEventListener('click', () => switchMode('chunked'));
    
    // 单文件上传事件
    singleUploadForm.addEventListener('submit', handleSingleUpload);
    
    // 分块上传事件
    chunkedUploadForm.addEventListener('submit', handleChunkedUpload);
    abortBtn.addEventListener('click', handleAbortUpload);
    
    // 清空日志事件
    clearLogBtn.addEventListener('click', clearLog);
});

// 切换上传模式
function switchMode(mode) {
    if (mode === 'single') {
        singleModeBtn.classList.add('active');
        chunkedModeBtn.classList.remove('active');
        singleUploadSection.style.display = 'block';
        chunkedUploadSection.style.display = 'none';
        addLog('📤 切换到单文件上传模式');
    } else {
        singleModeBtn.classList.remove('active');
        chunkedModeBtn.classList.add('active');
        singleUploadSection.style.display = 'none';
        chunkedUploadSection.style.display = 'block';
        addLog('📦 切换到分块上传模式');
    }
}

// 添加日志
function addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
    logContent.appendChild(logEntry);
    logContent.scrollTop = logContent.scrollHeight;
}

// 清空日志
function clearLog() {
    logContent.innerHTML = '';
    addLog('🧹 日志已清空');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化速度
function formatSpeed(bytesPerSecond) {
    return formatFileSize(bytesPerSecond) + '/s';
}

// 增强的错误处理函数
function handleUploadError(error, context = '') {
    let message = `❌ ${context}失败`;
    let details = '';
    
    if (error.response) {
        // HTTP错误响应
        details = `HTTP ${error.response.status}`;
        if (error.response.data && error.response.data.error) {
            details += `: ${error.response.data.error}`;
        }
    } else if (error.message) {
        details = error.message;
    } else {
        details = '未知错误';
    }
    
    addLog(`${message}: ${details}`, 'error');
    return { message, details };
}

// 重试机制函数
async function retryOperation(operation, maxRetries = 3, delay = 1000) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await operation();
        } catch (error) {
            lastError = error;
            
            if (attempt < maxRetries) {
                addLog(`⚠️ 操作失败，${delay}ms后重试 (${attempt}/${maxRetries})`, 'warning');
                await new Promise(resolve => setTimeout(resolve, delay));
                delay *= 2; // 指数退避
            }
        }
    }
    
    throw lastError;
}

// 单文件上传处理（优化版）
async function handleSingleUpload(e) {
    e.preventDefault();
    
    const file = singleFile.files[0];
    if (!file) {
        addLog('❌ 请选择文件', 'error');
        return;
    }
    
    // 基本文件验证
    if (file.size > 500 * 1024 * 1024) { // 500MB
        addLog('❌ 文件大小超过500MB限制，请使用分块上传', 'error');
        return;
    }
    
    if (file.size === 0) {
        addLog('❌ 不能上传空文件', 'error');
        return;
    }
    
    const uid = singleUid.value || 'anonymous';
    
    addLog(`📤 开始单文件上传: ${file.name} (${formatFileSize(file.size)})`);
    
    // 更新UI状态
    singleUploadBtn.disabled = true;
    singleUploadBtn.textContent = '上传中...';
    singleProgress.style.display = 'block';
    singleResult.innerHTML = '';
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('uid', uid);
    
    try {
        await new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
            let hasCompleted = false;
            
            // 设置超时
            const timeout = setTimeout(() => {
                if (!hasCompleted) {
                    xhr.abort();
                    reject(new Error('上传超时（2分钟）'));
                }
            }, 2 * 60 * 1000); // 2分钟超时
        
        // 上传进度
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percent = Math.round((e.loaded / e.total) * 100);
                singleProgressBar.style.width = percent + '%';
                singleProgressText.textContent = percent + '%';
            }
        });
        
        // 完成处理
        xhr.addEventListener('load', () => {
                hasCompleted = true;
                clearTimeout(timeout);
                
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                const result = JSON.parse(xhr.responseText);
                if (result.success) {
                    addLog('✅ 单文件上传成功!', 'success');
                    displaySingleResult(result);
                            resolve(result);
                } else {
                            const error = new Error(result.error || '上传失败');
                            error.details = result.details;
                            error.statusCode = xhr.status;
                            reject(error);
                    }
                    } catch (parseError) {
                        reject(new Error(`解析响应失败: ${parseError.message}`));
                }
            } else {
                    reject(new Error(`HTTP错误: ${xhr.status} ${xhr.statusText}`));
            }
        });
        
        // 错误处理
        xhr.addEventListener('error', () => {
                hasCompleted = true;
                clearTimeout(timeout);
                reject(new Error('网络连接错误'));
            });
            
            xhr.addEventListener('abort', () => {
                hasCompleted = true;
                clearTimeout(timeout);
                reject(new Error('上传被中止'));
        });
        
        xhr.open('POST', '/api/upload');
        xhr.send(formData);
        });
        
    } catch (error) {
        handleUploadError(error, '单文件上传');
        
        // 根据错误类型提供用户友好的建议
        if (error.message.includes('超时')) {
            addLog('💡 建议: 网络较慢时可尝试使用分块上传', 'info');
        } else if (error.message.includes('网络')) {
            addLog('💡 建议: 请检查网络连接后重试', 'info');
        }
    } finally {
        // 恢复UI状态
        singleUploadBtn.disabled = false;
        singleUploadBtn.textContent = '开始上传';
        singleProgress.style.display = 'none';
    }
}

// 显示单文件上传结果
function displaySingleResult(result) {
    singleResult.innerHTML = `
        <div class="result-success">
            <h3>✅ 上传成功</h3>
            <div class="result-details">
                <p><strong>📁 访问路径:</strong> ${result.prefix}/${result.path}</p>
                <p><strong>📏 文件大小:</strong> ${formatFileSize(result.size)}</p>
                <p><strong>🏷️ MIME类型:</strong> ${result.mime_type}</p>
                <p><strong>📂 生成文件名:</strong> ${result.filename}</p>
                <p><strong>💾 写入位置:</strong> ${result.written_paths.length} 个</p>
            </div>
            <div class="written-paths">
                <h4>💾 写入位置详情:</h4>
                <ul>
                    ${result.written_paths.map((path, index) => `
                        <li>
                            <strong>${index + 1}. [${path.type}]</strong> ${path.path}
                            ${path.bucket ? ` (bucket: ${path.bucket})` : ''}
                            - ${formatFileSize(path.size)}
                        </li>
                    `).join('')}
                </ul>
            </div>
        </div>
    `;
}

// 分块上传处理（优化版）
async function handleChunkedUpload(e) {
    e.preventDefault();
    
    const file = chunkedFile.files[0];
    if (!file) {
        addLog('❌ 请选择文件', 'error');
        return;
    }
    
    // 基本文件验证
    if (file.size > 10 * 1024 * 1024 * 1024) { // 10GB
        addLog('❌ 文件大小超过10GB限制', 'error');
        return;
    }
    
    if (file.size === 0) {
        addLog('❌ 不能上传空文件', 'error');
        return;
    }
    
    const uid = chunkedUid.value || 'anonymous';
    const chunkSizeBytes = parseInt(chunkSize.value);
    
    // 计算预估时间
    const totalChunks = Math.ceil(file.size / chunkSizeBytes);
    const estimatedTimeMinutes = Math.ceil(totalChunks * 0.1); // 每块约6秒的粗略估算
    
    addLog(`📦 开始分块上传: ${file.name} (${formatFileSize(file.size)}, 分块大小: ${formatFileSize(chunkSizeBytes)})`);
    addLog(`📊 共需上传 ${totalChunks} 个分块，预估时间: ${estimatedTimeMinutes} 分钟`, 'info');
    
    // 重置中止标志
    shouldAbortUpload = false;
    
    // 更新UI状态
    isUploading = true;
    uploadStartTime = Date.now();
    chunkedUploadBtn.disabled = true;
    chunkedUploadBtn.textContent = '上传中...';
    abortBtn.style.display = 'inline-block';
    chunkedProgress.style.display = 'block';
    chunkedResult.innerHTML = '';
    
    try {
        // 1. 初始化分块上传（带重试）
        const initResult = await retryOperation(async () => {
            if (shouldAbortUpload) throw new Error('用户中止上传');
            
            const response = await fetch('/api/chunked/init', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                filename: file.name,
                total_file_size: file.size,
                uid: uid
            })
        });
        
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error || '初始化失败');
        }
            
            return result;
        }, 3, 2000);
        
        currentUploadID = initResult.upload_id;
        addLog(`🚀 分块上传初始化成功: ${currentUploadID}`);
        
        // 2. 逐个上传分块
        let uploadedChunks = 0;
        let uploadedBytes = 0;
        let consecutiveFailures = 0;
        const maxConsecutiveFailures = 3;
        
        chunkInfo.textContent = `分块: 0/${totalChunks}`;
        uploadedSize.textContent = `已上传: 0 MB`;
        
        for (let i = 0; i < totalChunks; i++) {
            if (shouldAbortUpload) {
                throw new Error('用户中止上传');
            }
            
            const start = i * chunkSizeBytes;
            const end = Math.min(start + chunkSizeBytes, file.size);
            const chunk = file.slice(start, end);
            const chunkNumber = i + 1;
            
            addLog(`📦 上传分块 ${chunkNumber}/${totalChunks} (${formatFileSize(chunk.size)})`);
            
            try {
                // 上传单个分块（带重试）
                await retryOperation(async () => {
                    if (shouldAbortUpload) throw new Error('用户中止上传');
                    
                    const response = await fetch(`/api/chunked/upload?upload_id=${currentUploadID}&chunk_number=${chunkNumber}`, {
                method: 'POST',
                body: chunk
            });
            
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error || '分块上传失败');
            }
                    
                    return result;
                }, 3, 1000);
            
            uploadedChunks++;
            uploadedBytes += chunk.size;
                consecutiveFailures = 0; // 重置连续失败计数
            
            // 更新进度
            const percent = Math.round((uploadedBytes / file.size) * 100);
            chunkedProgressBar.style.width = percent + '%';
            chunkedProgressText.textContent = `${percent}% (${uploadedChunks}/${totalChunks})`;
            chunkInfo.textContent = `分块: ${uploadedChunks}/${totalChunks}`;
            uploadedSize.textContent = `已上传: ${formatFileSize(uploadedBytes)}`;
            
                // 计算速度和预估剩余时间
            const elapsed = (Date.now() - uploadStartTime) / 1000;
            const speed = uploadedBytes / elapsed;
                const remainingBytes = file.size - uploadedBytes;
                const estimatedRemainingTime = remainingBytes / speed;
                
                chunkedSpeedText.textContent = `${formatSpeed(speed)} | 剩余: ${Math.ceil(estimatedRemainingTime / 60)}分钟`;
                
            } catch (chunkError) {
                consecutiveFailures++;
                addLog(`⚠️ 分块 ${chunkNumber} 上传失败: ${chunkError.message}`, 'error');
                
                if (consecutiveFailures >= maxConsecutiveFailures) {
                    throw new Error(`连续 ${maxConsecutiveFailures} 个分块上传失败，终止上传`);
                }
                
                // 重试当前分块
                i--; // 重试当前索引
                await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
            }
        }
        
        // 3. 完成分块上传（带重试）
        addLog('🏁 正在完成分块上传...', 'info');
        const completeResult = await retryOperation(async () => {
            if (shouldAbortUpload) throw new Error('用户中止上传');
            
            const response = await fetch('/api/chunked/complete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                upload_id: currentUploadID,
                expected_chunks: totalChunks
            })
        });
        
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error || '完成上传失败');
        }
            
            return result;
        }, 3, 3000);
        
        addLog('✅ 分块上传完成!', 'success');
        displayChunkedResult(completeResult);
        
    } catch (error) {
        handleUploadError(error, '分块上传');
        
        // 尝试中止上传
        if (currentUploadID && !shouldAbortUpload) {
            try {
                await fetch('/api/chunked/abort', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        upload_id: currentUploadID
                    })
                });
                addLog('🛑 已自动中止失败的上传', 'warning');
            } catch (abortError) {
                addLog(`❌ 自动中止上传失败: ${abortError.message}`, 'error');
            }
        }
    } finally {
        // 恢复UI状态
        isUploading = false;
        shouldAbortUpload = false;
        currentUploadID = null;
        chunkedUploadBtn.disabled = false;
        chunkedUploadBtn.textContent = '开始分块上传';
        abortBtn.style.display = 'none';
        chunkedProgress.style.display = 'none';
    }
}

// 显示分块上传结果
function displayChunkedResult(result) {
    chunkedResult.innerHTML = `
        <div class="result-success">
            <h3>✅ 分块上传成功</h3>
            <div class="result-details">
                <p><strong>📁 访问路径:</strong> ${result.prefix}/${result.path}</p>
                <p><strong>📏 文件大小:</strong> ${formatFileSize(result.size)}</p>
                <p><strong>🏷️ MIME类型:</strong> ${result.mime_type}</p>
                <p><strong>📂 生成文件名:</strong> ${result.filename}</p>
                <p><strong>💾 写入位置:</strong> ${result.written_paths.length} 个</p>
            </div>
            <div class="written-paths">
                <h4>💾 写入位置详情:</h4>
                <ul>
                    ${result.written_paths.map((path, index) => `
                        <li>
                            <strong>${index + 1}. [${path.type}]</strong> ${path.path}
                            ${path.bucket ? ` (bucket: ${path.bucket})` : ''}
                            - ${formatFileSize(path.size)}
                        </li>
                    `).join('')}
                </ul>
            </div>
        </div>
    `;
}

// 中止分块上传（优化版）
async function handleAbortUpload() {
    if (!currentUploadID || !isUploading) {
        return;
    }
    
    addLog('🛑 正在中止分块上传...', 'warning');
    shouldAbortUpload = true; // 设置中止标志
    
    try {
        const response = await fetch('/api/chunked/abort', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                upload_id: currentUploadID
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        if (result.success) {
            addLog('✅ 分块上传已中止', 'warning');
        } else {
            addLog(`❌ 中止上传失败: ${result.error}`, 'error');
        }
    } catch (error) {
        addLog(`❌ 中止上传错误: ${error.message}`, 'error');
    }
    
    // 立即停止上传标志
    isUploading = false;
}

// ==================== 系统状态监控 ====================

// 检查MongoDB状态
async function checkMongoDBStatus() {
    try {
        updateStatusIndicator(mongodbIndicator, 'checking', '检查中...');
        mongodbDetails.innerHTML = '';

        const response = await fetch('/api/mongodb-status');
        const result = await response.json();

        if (result.mongodb.connected) {
            updateStatusIndicator(mongodbIndicator, 'success', '已连接');
            mongodbDetails.innerHTML = `
                <div class="status-detail">数据库: ${result.mongodb.database}</div>
                <div class="status-detail">时间: ${new Date(result.timestamp).toLocaleString()}</div>
                ${result.mongodb.test_collection ? '<div class="status-detail">✅ 集合访问正常</div>' : ''}
            `;
        } else {
            updateStatusIndicator(mongodbIndicator, 'error', '连接失败');
            mongodbDetails.innerHTML = `
                <div class="status-detail error">错误: ${result.mongodb.error || '未知错误'}</div>
                <div class="status-detail">时间: ${new Date(result.timestamp).toLocaleString()}</div>
            `;
        }
    } catch (error) {
        updateStatusIndicator(mongodbIndicator, 'error', '检查失败');
        mongodbDetails.innerHTML = `<div class="status-detail error">网络错误: ${error.message}</div>`;
    }
}

// 检查DirKeyStore状态
async function checkDirStoreStatus() {
    try {
        updateStatusIndicator(dirstoreIndicator, 'checking', '检查中...');
        dirstoreDetails.innerHTML = '';

        // 使用 /api/view-stats 端点来检查统计系统是否在工作，作为 DirKeyStore 状态的代理
        // 不再查询特定L1目录，只检查系统是否可以列出目录
        const response = await fetch('/api/view-stats'); 
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();

        // 检查结果中是否有预期的字段
        if (result && result.site) {
            updateStatusIndicator(dirstoreIndicator, 'success', '正常');
            dirstoreDetails.innerHTML = `
                <div class="status-detail success">DirKeyStore可用</div>
                <div class="status-detail">站点: ${result.site}</div>
                <div class="status-detail">目录数: ${result.count || 0}</div>
                <div class="status-detail">时间: ${new Date(result.timestamp).toLocaleString()}</div>
            `;
        } else {
            throw new Error('获取到的响应缺少预期字段');
        }
    } catch (error) {
        updateStatusIndicator(dirstoreIndicator, 'error', '错误');
        dirstoreDetails.innerHTML = `<div class="status-detail error">网络或服务错误: ${error.message}</div>`;
    }
}

// 更新状态指示器
function updateStatusIndicator(indicator, status, text) {
    const dot = indicator.querySelector('.status-dot');
    const textElement = indicator.querySelector('.status-text');

    // 移除所有状态类
    dot.classList.remove('success', 'error', 'warning', 'checking');

    // 添加新状态类
    dot.classList.add(status);
    textElement.textContent = text;
}

// 刷新所有状态
async function refreshAllStatus() {
    addLog('🔄 刷新系统状态...', 'info');
    await Promise.all([
        checkMongoDBStatus(),
        checkDirStoreStatus()
    ]);
    addLog('✅ 系统状态已更新', 'success');
}

// 加载L1目录列表
async function loadL1Directories() {
    statsDetails.innerHTML = '<div class="status-detail">加载L1目录中...</div>';
    loadL1Btn.disabled = true;

    try {
        // 不再需要传递站点参数，后端会使用配置中的站点
        const response = await fetch(`/api/view-stats`);
        const result = await response.json();

        if (response.ok && result.l1_directories) {
            statsL1Select.innerHTML = ''; // 清空旧选项
            if (result.count > 0) {
                result.l1_directories.forEach(l1 => {
                    const option = document.createElement('option');
                    option.value = l1;
                    option.textContent = l1;
                    statsL1Select.appendChild(option);
                });
                statsL1Select.style.display = 'inline-block';
                viewStatsBtn.style.display = 'inline-block';
                statsDetails.innerHTML = `<div class="status-detail success">成功加载 ${result.count} 个L1目录（站点: ${result.site}）</div>`;
                // 删除自动触发查看，让用户必须点击按钮才会查询
                // viewStats();
            } else {
                statsDetails.innerHTML = `<div class="status-detail warning">站点 ${result.site} 下未找到任何统计目录。</div>`;
                statsL1Select.style.display = 'none';
                viewStatsBtn.style.display = 'none';
            }
        } else {
            throw new Error(result.details || '加载L1目录失败');
        }
    } catch (error) {
        statsDetails.innerHTML = `<div class="status-detail error">加载错误: ${error.message}</div>`;
    } finally {
        loadL1Btn.disabled = false;
    }
}


// 查看统计数据
async function viewStats() {
    const l1 = statsL1Select.value; // 从下拉框获取L1

    if (!l1) {
        statsDetails.innerHTML = '<div class="status-detail error">请先加载并选择一个L1目录</div>';
        return;
    }

    try {
        statsDetails.innerHTML = '<div class="status-detail">查询中...</div>';

        // 只需要传递L1参数，后端会使用配置中的站点
        const response = await fetch(`/api/view-stats?l1=${l1}`);
        const result = await response.json();

        if (result.stats.found) {
            const summary = result.stats.summary;
            // The actual data is in result.stats.data, which is a single document
            const statsData = result.stats.data; 
            const l2Stats = statsData.l2Stats || {};

            let html = `
                <div class="stats-summary">
                    <h4>📊 统计摘要 (站点: ${result.site}, L1: ${result.l1})</h4>
                    <div class="summary-item">总文件数 (totalFiles): <strong>${statsData.totalFiles || 0}</strong></div>
                    <div class="summary-item">总实体数 (totalEntities): <strong>${statsData.totalEntities || 0}</strong></div>
                </div>
                <div class="stats-details-section">
                    <h4>📋 L2 目录详情</h4>
                    <div class="stats-table">
            `;
            
            const l2Keys = Object.keys(l2Stats);
            if (l2Keys.length > 0) {
                 l2Keys.sort().forEach((l2) => {
                    const stats = l2Stats[l2];
                    const entities = stats.l || 0; // 'l' for entityAmount
                    const files = stats.f || 0;    // 'f' for fileAmount

                    html += `
                        <div class="stats-row">
                            <div class="stats-cell"><strong>L2: ${l2}</strong></div>
                            <div class="stats-cell">实体: ${entities}</div>
                            <div class="stats-cell">文件: ${files}</div>
                        </div>
                    `;
                });
            } else {
                html += '<div class="status-detail warning">此 L1 目录下没有 L2 统计数据。</div>';
            }

            html += '</div></div>';
            statsDetails.innerHTML = html;

        } else {
            statsDetails.innerHTML = `
                <div class="status-detail warning">${result.stats.message || '未找到数据'}</div>
                <div class="status-detail">站点: ${result.site}, L1: ${result.l1}</div>
                <div class="status-detail">时间: ${new Date(result.timestamp).toLocaleString()}</div>
            `;
        }

        if (result.stats.error) {
            statsDetails.innerHTML = `<div class="status-detail error">错误: ${result.stats.error}</div>`;
        }

    } catch (error) {
        statsDetails.innerHTML = `<div class="status-detail error">网络错误: ${error.message}</div>`;
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 绑定刷新按钮事件
    if (refreshStatusBtn) {
        refreshStatusBtn.addEventListener('click', refreshAllStatus);
    }

    // 绑定查看统计按钮事件
    if (viewStatsBtn) {
        viewStatsBtn.addEventListener('click', viewStats);
    }

    // 绑定加载L1目录按钮事件
    if (loadL1Btn) {
        loadL1Btn.addEventListener('click', loadL1Directories);
    }

    // 站点为单一配置，无需切换监听
    
    // L1目录选择变化时自动查询
    if (statsL1Select) {
        statsL1Select.addEventListener('change', viewStats);
    }

    // 初始状态检查 - 只检查一次，不自动刷新
    setTimeout(refreshAllStatus, 1000);
    
    // 删除定期刷新 - 不再每30秒自动查询
    // setInterval(refreshAllStatus, 30000);
});
