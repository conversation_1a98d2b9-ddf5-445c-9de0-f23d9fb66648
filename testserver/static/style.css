* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2em;
    opacity: 0.9;
}

main {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 20px;
}

/* 模式选择器 */
.mode-selector {
    margin-bottom: 30px;
    text-align: center;
}

.mode-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

.mode-btn {
    padding: 12px 24px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mode-btn:hover {
    background: #f0f4ff;
    transform: translateY(-2px);
}

.mode-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.upload-section, .result-section, .log-section {
    margin-bottom: 30px;
}

.upload-section .description {
    color: #666;
    font-style: italic;
    margin-bottom: 20px;
}

.upload-section h2, .result-section h2, .log-section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5em;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #2d3748;
}

.config-description {
    margin-top: 8px;
    padding: 10px;
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    border-radius: 4px;
    font-size: 0.9em;
    color: #666;
    line-height: 1.4;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.file-info {
    margin-top: 10px;
    padding: 10px;
    background: #f7fafc;
    border-radius: 6px;
    border-left: 4px solid #4299e1;
}

.file-details {
    font-size: 14px;
    color: #4a5568;
}

button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
}

button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.abort-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    margin-left: 10px;
    width: auto;
    display: inline-block;
}

.abort-btn:hover {
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.clear-btn {
    background: linear-gradient(135deg, #ffa726 0%, #fb8c00 100%);
    font-size: 14px;
    padding: 8px 16px;
    margin-top: 10px;
    width: auto;
    display: inline-block;
}

.clear-btn:hover {
    box-shadow: 0 5px 15px rgba(255, 167, 38, 0.4);
}

.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.result-section {
    background: #f0fff4;
    border: 2px solid #9ae6b4;
    border-radius: 8px;
    padding: 20px;
}

.result-success h3 {
    color: #22543d;
    margin-bottom: 15px;
}

.result-details p {
    margin-bottom: 8px;
    color: #2f855a;
}

.written-paths {
    margin-top: 15px;
}

.written-paths h4 {
    color: #22543d;
    margin-bottom: 10px;
}

.written-paths ul {
    list-style: none;
    padding-left: 0;
}

.written-paths li {
    background: #e6fffa;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
    border-left: 3px solid #38b2ac;
    color: #234e52;
}

.log-section {
    background: #1a202c;
    border-radius: 8px;
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.log-section h2 {
    color: #e2e8f0;
    border-bottom-color: #4a5568;
}

#logContent {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 5px;
    padding: 5px 0;
}

.log-info {
    color: #e2e8f0;
}

.log-success {
    color: #9ae6b4;
}

.log-error {
    color: #feb2b2;
}

.timestamp {
    color: #a0aec0;
    font-size: 12px;
}

/* 进度条样式 */
.progress-container {
    margin: 20px 0;
    padding: 15px;
    background: #f7fafc;
    border-radius: 8px;
    border-left: 4px solid #4299e1;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: #4a5568;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4299e1, #667eea);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.chunk-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #718096;
    margin-top: 5px;
}

footer {
    text-align: center;
    color: white;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

footer p {
    font-size: 14px;
    line-height: 1.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    main {
        padding: 20px;
    }
}

/* ==================== 系统状态监控样式 ==================== */

.system-status {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.system-status h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5em;
    text-align: center;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.status-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-card h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.2em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-weight: 500;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    transition: all 0.3s ease;
}

.status-dot.success {
    background-color: #28a745;
    box-shadow: 0 0 8px rgba(40, 167, 69, 0.4);
}

.status-dot.error {
    background-color: #dc3545;
    box-shadow: 0 0 8px rgba(220, 53, 69, 0.4);
}

.status-dot.warning {
    background-color: #ffc107;
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.4);
}

.status-dot.checking {
    background-color: #6c757d;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-details {
    font-size: 0.9em;
    color: #6c757d;
}

.status-detail {
    margin-bottom: 5px;
    padding: 3px 0;
}

.status-detail.error {
    color: #dc3545;
    font-weight: 500;
}

.status-detail.warning {
    color: #856404;
    font-weight: 500;
}

.refresh-btn {
    display: block;
    margin: 0 auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.refresh-btn:active {
    transform: translateY(0);
}

/* 统计数据查看样式 */
.stats-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.stats-controls select,
.stats-controls input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9em;
}

.stats-controls input {
    flex: 1;
    min-width: 120px;
}

.view-stats-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.view-stats-btn:hover {
    background: #218838;
}

.stats-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    border-left: 4px solid #28a745;
}

.stats-summary h4 {
    margin-bottom: 10px;
    color: #495057;
}

.summary-item {
    margin-bottom: 5px;
    font-size: 0.9em;
}

.stats-details-section h4 {
    margin-bottom: 10px;
    color: #495057;
}

.stats-table {
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.stats-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 2fr;
    gap: 10px;
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.85em;
}

.stats-row:last-child {
    border-bottom: none;
}

.stats-row:nth-child(even) {
    background: #f8f9fa;
}

.stats-cell {
    padding: 2px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .status-grid {
        grid-template-columns: 1fr;
    }

    .system-status {
        padding: 20px;
        margin-bottom: 20px;
    }

    .stats-controls {
        flex-direction: column;
    }

    .stats-controls input,
    .stats-controls select,
    .view-stats-btn {
        width: 100%;
    }

    .stats-row {
        grid-template-columns: 1fr;
        gap: 5px;
    }

    .stats-cell {
        padding: 5px 0;
        border-bottom: 1px solid #eee;
    }

    .stats-cell:last-child {
        border-bottom: none;
    }
}
