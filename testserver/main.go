package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/real-rm/goconfig"
	levelStore "github.com/real-rm/golevelstore"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	// 单站点配置，简化设计
	currentSite           string                // 当前站点名称
	statsUpdater          goupload.StatsUpdater // 单文件上传的统计更新器
	chunkedStatsUpdater   goupload.StatsUpdater // 分块上传的统计更新器
	statsUpdaterMu        sync.RWMutex          // 保护统计更新器的并发访问
	chunkedStatsUpdaterMu sync.RWMutex          // 保护分块统计更新器的并发访问

	// s3ConfigCache 缓存S3配置，避免重复获取
	s3ConfigCache   map[string]levelStore.S3ProviderConfig
	s3ConfigCacheMu sync.RWMutex
	s3ConfigTime    time.Time
)

// UploadResponse 单文件上传响应结构
type UploadResponse struct {
	Success      bool                   `json:"success"`
	Path         string                 `json:"path"`
	Prefix       string                 `json:"prefix"`
	Size         int64                  `json:"size"`
	Filename     string                 `json:"filename"`
	MimeType     string                 `json:"mime_type"`
	WrittenPaths []goupload.WrittenPath `json:"written_paths"`
	Error        string                 `json:"error,omitempty"`
	Details      string                 `json:"details,omitempty"`
}

// ChunkedUploadInitResponse 分块上传初始化响应
type ChunkedUploadInitResponse struct {
	Success  bool   `json:"success"`
	UploadID string `json:"upload_id"`
	Error    string `json:"error,omitempty"`
	Details  string `json:"details,omitempty"`
}

// ChunkedUploadResponse 分块上传响应
type ChunkedUploadResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error,omitempty"`
	Details string `json:"details,omitempty"`
}

// ChunkedUploadCompleteResponse 分块上传完成响应
type ChunkedUploadCompleteResponse struct {
	Success      bool                   `json:"success"`
	Path         string                 `json:"path"`
	Prefix       string                 `json:"prefix"`
	Size         int64                  `json:"size"`
	Filename     string                 `json:"filename"`
	MimeType     string                 `json:"mime_type"`
	WrittenPaths []goupload.WrittenPath `json:"written_paths"`
	Error        string                 `json:"error,omitempty"`
	Details      string                 `json:"details,omitempty"`
}

// DeleteResponse 删除响应结构
type DeleteResponse struct {
	Success         bool                   `json:"success"`
	Path            string                 `json:"path"`
	DeletedPaths    []goupload.DeletedPath `json:"deleted_paths"`
	FailedPaths     []goupload.FailedPath  `json:"failed_paths,omitempty"`
	IsPartialDelete bool                   `json:"is_partial_delete"`
	Error           string                 `json:"error,omitempty"`
	Details         string                 `json:"details,omitempty"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Details string `json:"details,omitempty"`
}

func main() {
	// 设置配置文件路径
	configPath := "./config.toml"
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		log.Fatalf("配置文件不存在: %s", configPath)
	}

	// 设置环境变量，让 goconfig/golog 知道配置文件位置
	os.Setenv("RMBASE_FILE_CFG", configPath)
	log.Printf("📋 使用配置文件: %s", configPath)

	// 显式加载配置，确保在所有库初始化之前配置已准备好
	if err := goconfig.LoadConfig(); err != nil {
		log.Fatalf("❌ 无法加载配置文件: %v", err)
	}
	log.Printf("✅ 配置文件加载成功")

	// 读取站点配置
	userUploadConfig := goconfig.Config("userupload")
	if userUploadConfig == nil {
		log.Fatalf("❌ 配置文件中未找到 [userupload] 配置块")
	}

	userUploadMap, ok := userUploadConfig.(map[string]interface{})
	if !ok {
		log.Fatalf("❌ [userupload] 配置块格式无效")
	}

	siteValue, exists := userUploadMap["site"]
	if !exists || siteValue == nil {
		log.Fatalf("❌ 配置文件中未指定站点名称 (userupload.site)")
	}

	currentSite, ok = siteValue.(string)
	if !ok || currentSite == "" {
		log.Fatalf("❌ userupload.site 必须是非空字符串")
	}
	log.Printf("🏢 当前站点: %s", currentSite)

	// 集中初始化数据库和DirKeyStore
	if err := gomongo.InitMongoDB(); err != nil {
		log.Fatalf("❌ 无法初始化MongoDB: %v", err)
	}
	log.Printf("✅ MongoDB 初始化成功")

	statsColl := gomongo.Coll("tmp", "dir_stats")
	if statsColl == nil {
		log.Fatalf("❌ 无法获取 'dir_stats' 集合")
	}

	// 为不同的entryName创建不同的StatsUpdater实例
	testUploadStore, err := goupload.NewStatsUpdater(currentSite, "test_upload", statsColl)
	if err != nil {
		log.Fatalf("❌ 无法创建 '%s' 站点的test_upload DirKeyStore: %v", currentSite, err)
	}
	log.Printf("✅ '%s' 站点的test_upload DirKeyStore创建成功", currentSite)

	chunkedUploadStore, err := goupload.NewStatsUpdater(currentSite, "chunked_upload", statsColl)
	if err != nil {
		log.Fatalf("❌ 无法创建 '%s' 站点的chunked_upload DirKeyStore: %v", currentSite, err)
	}
	log.Printf("✅ '%s' 站点的chunked_upload DirKeyStore创建成功", currentSite)

	// 初始化全局变量
	s3ConfigCache = make(map[string]levelStore.S3ProviderConfig)
	setStatsUpdater(testUploadStore)           // 单文件上传使用
	setChunkedStatsUpdater(chunkedUploadStore) // 分块上传使用

	// 预加载S3配置
	if err := refreshS3Config(); err != nil {
		log.Printf("⚠️ 预加载S3配置失败，将在使用时加载: %v", err)
	}

	// 确保必要的根目录存在
	dirs := []string{"uploads", "uploads/files", "uploads/chunked", "temp", "temp/uploads", "temp/chunked", "logs"}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Fatalf("Failed to create directory %s: %v", dir, err)
		}
	}

	// 设置路由
	http.HandleFunc("/", serveIndex)

	// 单文件上传API
	http.HandleFunc("/api/upload", handleSingleUpload)

	// 分块上传API
	http.HandleFunc("/api/chunked/init", handleChunkedInit)
	http.HandleFunc("/api/chunked/upload", handleChunkedUpload)
	http.HandleFunc("/api/chunked/complete", handleChunkedComplete)
	http.HandleFunc("/api/chunked/abort", handleChunkedAbort)

	// 文件删除API
	http.HandleFunc("/api/delete", handleDelete)

	// 目录上传API
	http.HandleFunc("/api/directory/upload", handleDirectoryUpload)

	// MongoDB和DirKeyStore监控API
	http.HandleFunc("/api/mongodb-status", handleMongoDBStatus)
	http.HandleFunc("/api/view-stats", handleViewStats)

	// 静态文件服务
	http.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.Dir("static"))))
	http.Handle("/uploads/", http.StripPrefix("/uploads/", http.FileServer(http.Dir("uploads"))))

	port := "8080"
	if p := os.Getenv("PORT"); p != "" {
		port = p
	}

	log.Printf("🚀 goupload 测试服务器启动在端口 %s", port)
	log.Printf("📱 访问地址: http://localhost:%s", port)
	log.Printf("🏢 服务站点: %s", currentSite)
	log.Printf("📁 上传目录: %s", filepath.Join(".", "uploads"))
	log.Printf("🔧 API 端点:")
	log.Printf("   单文件上传: POST /api/upload")
	log.Printf("   目录上传:   POST /api/directory/upload")
	log.Printf("   分块初始化: POST /api/chunked/init")
	log.Printf("   分块上传:   POST /api/chunked/upload")
	log.Printf("   分块完成:   POST /api/chunked/complete")
	log.Printf("   分块中止:   POST /api/chunked/abort")
	log.Printf("   文件删除:   DELETE /api/delete")

	if err := http.ListenAndServe(":"+port, nil); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

// setStatsUpdater 安全设置统计更新器
func setStatsUpdater(updater goupload.StatsUpdater) {
	statsUpdaterMu.Lock()
	defer statsUpdaterMu.Unlock()
	statsUpdater = updater
}

// getStatsUpdater 安全获取统计更新器
func getStatsUpdater() goupload.StatsUpdater {
	statsUpdaterMu.RLock()
	defer statsUpdaterMu.RUnlock()
	return statsUpdater
}

// setChunkedStatsUpdater 安全设置分块上传统计更新器
func setChunkedStatsUpdater(updater goupload.StatsUpdater) {
	chunkedStatsUpdaterMu.Lock()
	defer chunkedStatsUpdaterMu.Unlock()
	chunkedStatsUpdater = updater
}

// getChunkedStatsUpdater 安全获取分块上传统计更新器
func getChunkedStatsUpdater() goupload.StatsUpdater {
	chunkedStatsUpdaterMu.RLock()
	defer chunkedStatsUpdaterMu.RUnlock()
	return chunkedStatsUpdater
}

// getCurrentSite 获取当前站点名称
func getCurrentSite() string {
	return currentSite // 只读，无需锁保护
}

// refreshS3Config 刷新S3配置缓存
func refreshS3Config() error {
	s3ProviderMap, err := levelStore.GetConnectionSources(nil)
	if err != nil {
		return err
	}

	s3ConfigCacheMu.Lock()
	defer s3ConfigCacheMu.Unlock()

	// 清空旧缓存
	for k := range s3ConfigCache {
		delete(s3ConfigCache, k)
	}

	// 添加新配置
	if s3ProviderMap != nil {
		for _, p := range s3ProviderMap.S3Providers {
			s3ConfigCache[p.Name] = p
		}
	}
	s3ConfigTime = time.Now()

	log.Printf("✅ S3配置缓存已更新，共 %d 个提供商", len(s3ConfigCache))
	return nil
}

// getS3Config 获取S3配置（带缓存）
func getS3Config() (map[string]levelStore.S3ProviderConfig, error) {
	s3ConfigCacheMu.RLock()

	// 检查缓存是否过期（5分钟）
	if time.Since(s3ConfigTime) < 5*time.Minute && len(s3ConfigCache) > 0 {
		// 返回缓存副本
		result := make(map[string]levelStore.S3ProviderConfig)
		for k, v := range s3ConfigCache {
			result[k] = v
		}
		s3ConfigCacheMu.RUnlock()
		return result, nil
	}
	s3ConfigCacheMu.RUnlock()

	// 缓存过期，重新加载
	if err := refreshS3Config(); err != nil {
		return nil, fmt.Errorf("刷新S3配置失败: %w", err)
	}

	s3ConfigCacheMu.RLock()
	defer s3ConfigCacheMu.RUnlock()

	result := make(map[string]levelStore.S3ProviderConfig)
	for k, v := range s3ConfigCache {
		result[k] = v
	}
	return result, nil
}

// serveIndex 提供主页
func serveIndex(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}
	http.ServeFile(w, r, "static/index.html")
}

// setCORSHeaders 设置CORS头
func setCORSHeaders(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
	w.Header().Set("Access-Control-Max-Age", "86400")
}

// sendError 发送错误响应
func sendError(w http.ResponseWriter, message string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(ErrorResponse{
		Success: false,
		Error:   message,
	})
}

// sendErrorWithDetails 发送带详细信息的错误响应
func sendErrorWithDetails(w http.ResponseWriter, message, details string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(ErrorResponse{
		Success: false,
		Error:   message,
		Details: details,
	})
}

// handleSingleUpload 处理单文件上传（单站点优化版）
func handleSingleUpload(w http.ResponseWriter, r *http.Request) {
	setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		sendError(w, "只支持POST方法", http.StatusMethodNotAllowed)
		return
	}

	// 解析multipart form
	err := r.ParseMultipartForm(32 << 20) // 32MB max memory
	if err != nil {
		sendError(w, "解析表单失败", http.StatusBadRequest)
		return
	}

	// 获取文件
	file, header, err := r.FormFile("file")
	if err != nil {
		sendError(w, "获取文件失败", http.StatusBadRequest)
		return
	}
	defer func() {
		if closeErr := file.Close(); closeErr != nil {
			log.Printf("⚠️ 关闭上传文件失败: %v", closeErr)
		}
	}()

	// 获取用户ID（可选）
	uid := r.FormValue("uid")
	if uid == "" {
		uid = "anonymous"
	}

	site := getCurrentSite()
	log.Printf("📤 开始单文件上传: %s (站点: %s, 用户: %s)", header.Filename, site, uid)

	// 调用goupload进行上传
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	// 获取统计更新器
	dirStore := getStatsUpdater()
	if dirStore == nil {
		log.Printf("❌ 统计更新器未初始化")
		sendErrorWithDetails(w, "上传失败", "服务器内部错误：统计系统未初始化", http.StatusInternalServerError)
		return
	}

	result, err := goupload.Upload(ctx, dirStore, site, "test_upload", uid, file, header.Filename, 0)
	if err != nil {
		log.Printf("❌ 单文件上传失败: %v", err)

		// 根据错误消息内容返回合适的状态码
		var statusCode int
		var message string
		errMsg := err.Error()

		if strings.Contains(errMsg, "filename") ||
			strings.Contains(errMsg, "exceeds limit") ||
			strings.Contains(errMsg, "cannot be zero") ||
			strings.Contains(errMsg, "config") {
			statusCode = http.StatusBadRequest
			message = "文件验证失败"
		} else if strings.Contains(errMsg, "write to") ||
			strings.Contains(errMsg, "failed to write") {
			statusCode = http.StatusInternalServerError
			message = "文件写入失败"
		} else if strings.Contains(errMsg, "rollback") {
			statusCode = http.StatusInternalServerError
			message = "上传失败，清理时发生错误"
		} else {
			statusCode = http.StatusInternalServerError
			message = "上传失败"
		}

		sendErrorWithDetails(w, message, err.Error(), statusCode)
		return
	}

	log.Printf("✅ 单文件上传成功: %s -> %s -> %s", header.Filename, result.Prefix, result.Path)

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	response := UploadResponse{
		Success:      true,
		Path:         result.Path,
		Prefix:       result.Prefix,
		Size:         result.Size,
		Filename:     result.Filename,
		MimeType:     result.MimeType,
		WrittenPaths: result.WrittenPaths,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("❌ 编码响应失败: %v", err)
	}
}

// handleChunkedInit 处理分块上传初始化（单站点优化版）
func handleChunkedInit(w http.ResponseWriter, r *http.Request) {
	setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		sendError(w, "只支持POST方法", http.StatusMethodNotAllowed)
		return
	}

	// 解析JSON请求
	var req struct {
		Filename      string `json:"filename"`
		TotalFileSize int64  `json:"total_file_size"`
		UID           string `json:"uid"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendError(w, "解析请求失败", http.StatusBadRequest)
		return
	}

	if req.Filename == "" {
		sendError(w, "文件名不能为空", http.StatusBadRequest)
		return
	}

	if req.TotalFileSize <= 0 {
		sendError(w, "文件大小必须大于0", http.StatusBadRequest)
		return
	}

	if req.UID == "" {
		req.UID = "anonymous"
	}

	site := getCurrentSite()
	log.Printf("🚀 初始化分块上传: %s (站点: %s, 大小: %d, 用户: %s)", req.Filename, site, req.TotalFileSize, req.UID)

	// 调用goupload初始化分块上传
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	provider := &goupload.LevelStoreProvider{}
	uploadID, err := goupload.InitiateChunkedUpload(ctx, provider, site, "chunked_upload", req.UID, req.Filename, req.TotalFileSize)
	if err != nil {
		log.Printf("❌ 初始化分块上传失败: %v", err)

		// 根据错误消息内容返回合适的状态码
		var statusCode int
		var message string
		errMsg := err.Error()

		if strings.Contains(errMsg, "filename") ||
			strings.Contains(errMsg, "exceeds limit") ||
			strings.Contains(errMsg, "cannot be zero") ||
			strings.Contains(errMsg, "config") {
			statusCode = http.StatusBadRequest
			message = "分块上传参数验证失败"
		} else if strings.Contains(errMsg, "write to") ||
			strings.Contains(errMsg, "failed to write") {
			statusCode = http.StatusInternalServerError
			message = "初始化分块上传失败"
		} else {
			statusCode = http.StatusInternalServerError
			message = "初始化分块上传失败"
		}

		sendErrorWithDetails(w, message, err.Error(), statusCode)
		return
	}

	log.Printf("✅ 分块上传初始化成功: %s", uploadID)

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	response := ChunkedUploadInitResponse{
		Success:  true,
		UploadID: uploadID,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("❌ 编码响应失败: %v", err)
	}
}

// handleChunkedUpload 处理分块上传
func handleChunkedUpload(w http.ResponseWriter, r *http.Request) {
	setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		sendError(w, "只支持POST方法", http.StatusMethodNotAllowed)
		return
	}

	// 获取URL参数
	uploadID := r.URL.Query().Get("upload_id")
	chunkNumberStr := r.URL.Query().Get("chunk_number")

	if uploadID == "" {
		sendError(w, "缺少upload_id参数", http.StatusBadRequest)
		return
	}

	if chunkNumberStr == "" {
		sendError(w, "缺少chunk_number参数", http.StatusBadRequest)
		return
	}

	chunkNumber, err := strconv.Atoi(chunkNumberStr)
	if err != nil || chunkNumber <= 0 {
		sendError(w, "无效的chunk_number", http.StatusBadRequest)
		return
	}

	log.Printf("📦 上传分块: %s 块号: %d", uploadID, chunkNumber)

	// 调用goupload上传分块
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	err = goupload.UploadChunk(ctx, uploadID, chunkNumber, r.Body)
	if err != nil {
		log.Printf("❌ 分块上传失败: %v", err)

		// 根据错误消息内容返回合适的状态码
		var statusCode int
		var message string
		errMsg := err.Error()

		if strings.Contains(errMsg, "invalid chunk number") ||
			strings.Contains(errMsg, "exceeds the maximum") ||
			strings.Contains(errMsg, "invalid or has expired") ||
			strings.Contains(errMsg, "already been finalized") {
			statusCode = http.StatusBadRequest
			message = "分块数据验证失败"
		} else if strings.Contains(errMsg, "failed to write") ||
			strings.Contains(errMsg, "failed to create") {
			statusCode = http.StatusInternalServerError
			message = "分块数据写入失败"
		} else {
			statusCode = http.StatusInternalServerError
			message = "分块上传失败"
		}

		sendErrorWithDetails(w, message, err.Error(), statusCode)
		return
	}

	log.Printf("✅ 分块上传成功: %s 块号: %d", uploadID, chunkNumber)

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	response := ChunkedUploadResponse{
		Success: true,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("❌ 编码响应失败: %v", err)
	}
}

// handleChunkedComplete 处理分块上传完成（单站点优化版）
func handleChunkedComplete(w http.ResponseWriter, r *http.Request) {
	setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		sendError(w, "只支持POST方法", http.StatusMethodNotAllowed)
		return
	}

	// 解析JSON请求
	var req struct {
		UploadID       string `json:"upload_id"`
		ExpectedChunks int    `json:"expected_chunks"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendError(w, "解析请求失败", http.StatusBadRequest)
		return
	}

	if req.UploadID == "" {
		sendError(w, "缺少upload_id", http.StatusBadRequest)
		return
	}

	if req.ExpectedChunks <= 0 {
		sendError(w, "expected_chunks必须大于0", http.StatusBadRequest)
		return
	}

	site := getCurrentSite()
	log.Printf("🏁 完成分块上传: %s (站点: %s, 预期块数: %d)", req.UploadID, site, req.ExpectedChunks)

	// 获取S3提供商配置（使用缓存）
	s3Map, err := getS3Config()
	if err != nil {
		log.Printf("❌ 获取S3配置失败: %v", err)
		sendErrorWithDetails(w, "获取S3配置失败", err.Error(), http.StatusInternalServerError)
		return
	}

	// 调用goupload完成分块上传
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
	defer cancel()

	// 获取分块上传统计更新器
	dirStore := getChunkedStatsUpdater()
	if dirStore == nil {
		log.Printf("❌ 分块上传统计更新器未初始化")
		sendErrorWithDetails(w, "完成分块上传失败", "服务器内部错误：分块统计系统未初始化", http.StatusInternalServerError)
		return
	}

	result, err := goupload.CompleteChunkedUpload(ctx, dirStore, req.UploadID, req.ExpectedChunks, s3Map)
	if err != nil {
		log.Printf("❌ 完成分块上传失败: %v", err)

		// 根据错误消息内容返回合适的状态码
		var statusCode int
		var message string
		errMsg := err.Error()

		if strings.Contains(errMsg, "invalid or has expired") ||
			strings.Contains(errMsg, "already been finalized") ||
			strings.Contains(errMsg, "missing chunk") ||
			strings.Contains(errMsg, "does not match declared") {
			statusCode = http.StatusBadRequest
			message = "分块验证失败"
		} else if strings.Contains(errMsg, "write to") ||
			strings.Contains(errMsg, "failed to write") {
			statusCode = http.StatusInternalServerError
			message = "分块合并失败"
		} else if strings.Contains(errMsg, "rollback") {
			statusCode = http.StatusInternalServerError
			message = "上传失败，清理时发生错误"
		} else {
			statusCode = http.StatusInternalServerError
			message = "完成分块上传失败"
		}

		sendErrorWithDetails(w, message, err.Error(), statusCode)
		return
	}

	log.Printf("✅ 分块上传完成: %s -> %s", req.UploadID, result.Path)

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	response := ChunkedUploadCompleteResponse{
		Success:      true,
		Path:         result.Path,
		Prefix:       result.Prefix,
		Size:         result.Size,
		Filename:     result.Filename,
		MimeType:     result.MimeType,
		WrittenPaths: result.WrittenPaths,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("❌ 编码响应失败: %v", err)
	}
}

// handleChunkedAbort 处理分块上传中止
func handleChunkedAbort(w http.ResponseWriter, r *http.Request) {
	setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		sendError(w, "只支持POST方法", http.StatusMethodNotAllowed)
		return
	}

	// 解析JSON请求
	var req struct {
		UploadID string `json:"upload_id"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendError(w, "解析请求失败", http.StatusBadRequest)
		return
	}

	if req.UploadID == "" {
		sendError(w, "缺少upload_id", http.StatusBadRequest)
		return
	}

	log.Printf("🛑 中止分块上传: %s", req.UploadID)

	// 调用goupload中止分块上传
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err := goupload.AbortChunkedUpload(ctx, req.UploadID)
	if err != nil {
		log.Printf("❌ 中止分块上传失败: %v", err)
		sendErrorWithDetails(w, "中止分块上传失败", err.Error(), http.StatusInternalServerError)
		return
	}

	log.Printf("✅ 分块上传已中止: %s", req.UploadID)

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	response := ChunkedUploadResponse{
		Success: true,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("❌ 编码响应失败: %v", err)
	}
}

// handleMongoDBStatus 检查MongoDB连接状态
func handleMongoDBStatus(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	response := map[string]interface{}{
		"timestamp": time.Now().Format(time.RFC3339),
		"mongodb": map[string]interface{}{
			"connected": false,
			"error":     nil,
			"database":  "tmp",
		},
	}

	// 不再调用 gomongo.InitMongoDB()。连接应该在 main() 中全局初始化一次。
	// 通过尝试获取一个 collection 来检查连接状态。
	// 这是一个被动的检查，不会干扰现有连接池。
	coll := gomongo.Coll("tmp", "connection_test")
	if coll != nil {
		// 如果能成功获取 collection，我们假设连接是正常的。
		// 一个更健壮的检查会执行一个 ping 操作。
		response["mongodb"].(map[string]interface{})["connected"] = true
		response["mongodb"].(map[string]interface{})["test_collection"] = "accessible"
	} else {
		// 如果无法获取 collection，说明连接可能已经断开。
		response["mongodb"].(map[string]interface{})["connected"] = false
		response["mongodb"].(map[string]interface{})["error"] = "无法获取MongoDB collection，连接可能已断开"
	}

	json.NewEncoder(w).Encode(response)
}

// handleListL1Dirs 是一个辅助函数，用于查询并返回指定站点下所有唯一的L1目录
func handleListL1Dirs(w http.ResponseWriter, coll *gomongo.MongoCollection, site string) {
	ctx := context.Background()
	// 修正这里的查询，以匹配嵌套在_id中的字段
	filter := bson.M{"_id.board": site}

	// 修正要查询的字段名称
	l1Values, err := coll.Distinct(ctx, "_id.l1", filter)
	if err != nil {
		sendErrorWithDetails(w, "查询L1目录列表失败", err.Error(), http.StatusInternalServerError)
		return
	}

	var l1s []string
	if l1Values != nil {
		for _, v := range l1Values {
			if s, ok := v.(string); ok {
				l1s = append(l1s, s)
			}
		}
	} else {
		// 如果l1Values为nil（例如，Distinct出错但未返回error），确保l1s是一个空切片而不是nil
		l1s = []string{}
	}

	response := map[string]interface{}{
		"timestamp":      time.Now().Format(time.RFC3339),
		"site":           site,
		"l1_directories": l1s,
		"count":          len(l1s),
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleViewStats 查看MongoDB中的实际统计数据（单站点优化版）
func handleViewStats(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	// 使用配置中的当前站点，不再支持通过参数指定站点
	site := getCurrentSite()
	l1 := r.URL.Query().Get("l1")

	// 获取collection
	coll := gomongo.Coll("tmp", "dir_stats")
	if coll == nil {
		sendErrorWithDetails(w, "获取统计集合失败", "无法获取dir_stats集合", http.StatusInternalServerError)
		return
	}

	// 如果l1参数为空，则查询并返回该站点所有唯一的l1目录
	if l1 == "" {
		handleListL1Dirs(w, coll, site)
		return
	}

	response := map[string]interface{}{
		"timestamp": time.Now().Format(time.RFC3339),
		"site":      site,
		"l1":        l1,
		"stats": map[string]interface{}{
			"found": false,
			"error": nil,
		},
	}

	// 查询统计数据
	ctx := context.Background()
	filter := bson.M{
		"_id": bson.M{
			"board": site,
			"l1":    l1,
		},
	}

	cursor, err := coll.Find(ctx, filter)
	if err != nil {
		response["stats"].(map[string]interface{})["error"] = fmt.Sprintf("查询失败: %v", err)
		json.NewEncoder(w).Encode(response)
		return
	}
	defer cursor.Close(ctx)

	var results []map[string]interface{}
	// If you only expect one document, you could use FindOne.
	// For this example, we'll keep it as Find to handle potential duplicates,
	// though the _id should be unique.
	if err = cursor.All(ctx, &results); err != nil {
		response["stats"].(map[string]interface{})["error"] = fmt.Sprintf("读取结果失败: %v", err)
		json.NewEncoder(w).Encode(response)
		return
	}

	if len(results) > 0 {
		response["stats"].(map[string]interface{})["found"] = true
		response["stats"].(map[string]interface{})["count"] = len(results)
		// Assuming we only care about the first result since _id is unique
		firstResult := results[0]
		response["stats"].(map[string]interface{})["data"] = firstResult

		// 计算总计
		totalFiles := 0
		if files, ok := firstResult["totalFiles"].(int32); ok { // Changed from result["files"]
			totalFiles = int(files)
		}
		response["stats"].(map[string]interface{})["summary"] = map[string]interface{}{
			"total_files": totalFiles,
		}
	} else {
		response["stats"].(map[string]interface{})["found"] = false
		response["stats"].(map[string]interface{})["message"] = fmt.Sprintf("未找到站点 %s L1目录 %s 的统计数据", site, l1)
	}

	json.NewEncoder(w).Encode(response)
}

// handleDelete 处理文件删除请求
func handleDelete(w http.ResponseWriter, r *http.Request) {
	setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "DELETE" {
		sendError(w, "只支持DELETE方法", http.StatusMethodNotAllowed)
		return
	}

	// 解析JSON请求
	var req struct {
		EntryName    string `json:"entry_name"`    // 上传类型 (test_upload 或 chunked_upload)
		RelativePath string `json:"relative_path"` // 相对路径，如 "1279/644ae/IS578xe_1_UCycCOd6DO_.jpeg"
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendError(w, "解析请求失败", http.StatusBadRequest)
		return
	}

	if req.EntryName == "" {
		sendError(w, "entry_name不能为空", http.StatusBadRequest)
		return
	}

	if req.RelativePath == "" {
		sendError(w, "relative_path不能为空", http.StatusBadRequest)
		return
	}

	site := getCurrentSite()
	log.Printf("🗑️ 开始删除文件: %s (站点: %s, 类型: %s)", req.RelativePath, site, req.EntryName)

	// 调用goupload进行删除
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	// 获取统计更新器
	dirStore := getStatsUpdater()
	if dirStore == nil {
		log.Printf("❌ 统计更新器未初始化")
		sendErrorWithDetails(w, "删除失败", "服务器内部错误：统计系统未初始化", http.StatusInternalServerError)
		return
	}

	result, err := goupload.Delete(ctx, dirStore, site, req.EntryName, req.RelativePath)
	if err != nil {
		log.Printf("❌ 文件删除失败: %v", err)

		// 根据错误消息内容返回合适的状态码
		var statusCode int
		var message string
		errMsg := err.Error()

		if strings.Contains(errMsg, "config") ||
			strings.Contains(errMsg, "not found") {
			statusCode = http.StatusBadRequest
			message = "删除参数验证失败"
		} else if strings.Contains(errMsg, "all delete operations failed") {
			statusCode = http.StatusInternalServerError
			message = "所有删除操作都失败了"
		} else {
			statusCode = http.StatusInternalServerError
			message = "删除失败"
		}

		sendErrorWithDetails(w, message, err.Error(), statusCode)
		return
	}

	// 记录删除结果
	if result.IsPartialDelete {
		log.Printf("⚠️ 部分删除成功: %s (成功: %d, 失败: %d)",
			req.RelativePath, len(result.DeletedPaths), len(result.FailedPaths))
	} else {
		log.Printf("✅ 文件删除成功: %s (删除位置: %d)",
			req.RelativePath, len(result.DeletedPaths))
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	response := DeleteResponse{
		Success:         true,
		Path:            result.Path,
		DeletedPaths:    result.DeletedPaths,
		FailedPaths:     result.FailedPaths,
		IsPartialDelete: result.IsPartialDelete,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("❌ 编码响应失败: %v", err)
	}
}

// DirectoryUploadResponse 目录上传响应结构
type DirectoryUploadResponse struct {
	Success         bool                          `json:"success"`
	DirectoryPath   string                        `json:"directory_path"`
	TotalFiles      int                           `json:"total_files"`
	TotalSize       int64                         `json:"total_size"`
	SuccessFiles    []string                      `json:"success_files"`
	FailedFiles     []goupload.DirectoryFileError `json:"failed_files,omitempty"`
	WrittenPaths    []goupload.WrittenPath        `json:"written_paths"`
	IsPartialUpload bool                          `json:"is_partial_upload"`
	Error           string                        `json:"error,omitempty"`
	Details         string                        `json:"details,omitempty"`
}

// handleDirectoryUpload 处理目录上传请求
func handleDirectoryUpload(w http.ResponseWriter, r *http.Request) {
	setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		sendError(w, "只支持POST方法", http.StatusMethodNotAllowed)
		return
	}

	// 解析multipart form
	err := r.ParseMultipartForm(100 << 20) // 100MB max memory
	if err != nil {
		sendError(w, "解析表单失败", http.StatusBadRequest)
		return
	}

	// 获取参数
	userID := r.FormValue("uid")
	if userID == "" {
		userID = "anonymous"
	}

	directoryName := r.FormValue("directory_name")
	if directoryName == "" {
		sendError(w, "directory_name不能为空", http.StatusBadRequest)
		return
	}

	// 获取最大大小限制（可选）
	maxSizeStr := r.FormValue("max_size")
	var maxSize int64 = 0
	if maxSizeStr != "" {
		if size, err := strconv.ParseInt(maxSizeStr, 10, 64); err == nil {
			maxSize = size
		}
	}

	site := getCurrentSite()
	log.Printf("📁 开始目录上传: %s (站点: %s, 用户: %s)", directoryName, site, userID)

	// 构建文件列表
	var files []goupload.DirectoryFileEntry
	for fieldName, fileHeaders := range r.MultipartForm.File {
		for _, fileHeader := range fileHeaders {
			file, err := fileHeader.Open()
			if err != nil {
				log.Printf("⚠️ 无法打开文件 %s: %v", fileHeader.Filename, err)
				continue
			}
			defer file.Close()

			// 获取相对路径
			relativePath := fileHeader.Filename

			// 检查是否有webkitRelativePath（HTML5目录上传）
			if webkitPath := fileHeader.Header.Get("webkitRelativePath"); webkitPath != "" {
				relativePath = webkitPath
			}

			// 如果字段名包含路径信息，也可以使用
			if fieldName != "files" && fieldName != "file" {
				relativePath = fieldName
			}

			// 检查Content-Disposition header中的filename参数
			// 这可以包含完整的相对路径
			if disposition := fileHeader.Header.Get("Content-Disposition"); disposition != "" {
				if _, params, err := mime.ParseMediaType(disposition); err == nil {
					if filename, ok := params["filename"]; ok && filename != "" {
						relativePath = filename
					}
				}
			}

			files = append(files, goupload.DirectoryFileEntry{
				RelativePath: relativePath,
				Reader:       file,
				Size:         fileHeader.Size,
			})
		}
	}

	if len(files) == 0 {
		sendError(w, "没有找到要上传的文件", http.StatusBadRequest)
		return
	}

	log.Printf("📁 准备上传 %d 个文件", len(files))

	// 构建上传请求
	request := &goupload.DirectoryUploadRequest{
		Site:          site,
		EntryName:     "test_upload", // 使用与单文件上传相同的entryName
		UserID:        userID,
		DirectoryName: directoryName,
		Files:         files,
		MaxSize:       maxSize,
	}

	// 执行上传
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// 获取统计更新器
	dirStore := getStatsUpdater()
	if dirStore == nil {
		log.Printf("❌ 统计更新器未初始化")
		sendErrorWithDetails(w, "目录上传失败", "服务器内部错误：统计系统未初始化", http.StatusInternalServerError)
		return
	}

	result, err := goupload.UploadDirectory(ctx, dirStore, request)
	if err != nil {
		log.Printf("❌ 目录上传失败: %v", err)

		// 根据错误消息内容返回合适的状态码
		var statusCode int
		var message string
		errMsg := err.Error()

		if strings.Contains(errMsg, "cannot be empty") ||
			strings.Contains(errMsg, "exceeds limit") ||
			strings.Contains(errMsg, "path traversal") ||
			strings.Contains(errMsg, "config") {
			statusCode = http.StatusBadRequest
			message = "目录上传参数验证失败"
		} else if strings.Contains(errMsg, "write to") ||
			strings.Contains(errMsg, "failed to write") {
			statusCode = http.StatusInternalServerError
			message = "目录写入失败"
		} else if strings.Contains(errMsg, "all files failed") {
			statusCode = http.StatusInternalServerError
			message = "所有文件上传都失败了"
		} else {
			statusCode = http.StatusInternalServerError
			message = "目录上传失败"
		}

		sendErrorWithDetails(w, message, err.Error(), statusCode)
		return
	}

	// 记录上传结果
	if result.IsPartialUpload {
		log.Printf("⚠️ 部分目录上传成功: %s (成功: %d, 失败: %d)",
			directoryName, len(result.SuccessFiles), len(result.FailedFiles))
	} else {
		log.Printf("✅ 目录上传成功: %s -> %s (文件数: %d, 总大小: %d)",
			directoryName, result.DirectoryPath, result.TotalFiles, result.TotalSize)
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	response := DirectoryUploadResponse{
		Success:         true,
		DirectoryPath:   result.DirectoryPath,
		TotalFiles:      result.TotalFiles,
		TotalSize:       result.TotalSize,
		SuccessFiles:    result.SuccessFiles,
		FailedFiles:     result.FailedFiles,
		WrittenPaths:    result.WrittenPaths,
		IsPartialUpload: result.IsPartialUpload,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("❌ 编码响应失败: %v", err)
	}
}
