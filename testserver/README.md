# GoUpload 测试服务器

这是一个完整的测试服务器，用于演示和测试 goupload 库的单文件上传、目录上传和分块上传功能。

## 🚀 快速开始

### 1. 启动服务器

```bash
# 使用启动脚本（推荐）
./start-server.sh

# 或直接运行
go run main.go

# 或编译后运行
go build -o testserver main.go
./testserver
```

### 2. 访问测试页面

打开浏览器访问: [http://localhost:8080](http://localhost:8080)

## 📋 功能特性

### 🔄 多模式支持

**单文件上传模式:**
- 适用于小于500MB的文件
- 简单快速的上传体验
- 实时进度显示
- 支持本地存储 + S3存储

**目录上传模式:**
- 支持整个目录的批量上传
- 保持原始目录结构
- 自动处理文件流和本地文件
- 防止路径遍历攻击
- 支持大小限制和进度显示

**分块上传模式:**
- 适用于大文件（最大10GB）
- 支持断点续传
- 可配置分块大小（1MB-20MB）
- 详细的分块进度显示
- 支持中止上传功能

### 🎯 API 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/upload` | POST | 单文件上传 |
| `/api/directory/upload` | POST | 目录上传 |
| `/api/chunked/init` | POST | 初始化分块上传 |
| `/api/chunked/upload` | POST | 上传分块 |
| `/api/chunked/complete` | POST | 完成分块上传 |
| `/api/chunked/abort` | POST | 中止分块上传 |

## 🔧 配置说明

服务器会自动读取当前目录下的 `config.toml` 文件。配置文件包含：

- **服务器配置**: 端口、主机等
- **存储配置**: 本地存储和S3存储设置
- **上传限制**: 文件大小、类型限制等
- **日志配置**: 日志级别和输出格式

### 配置文件示例

```toml
[server]
port = 8080
host = "localhost"

# S3 配置 (GarageHQ)
[connection_sources]
  [[connection_sources.s3_providers]]
    name = "garage-primary"
    endpoint = "http://localhost:3900"
    key = "你的密钥ID"
    pass = "你的密钥密码"
    region = "garage"

# 上传配置
[userupload]
site = "TEST"

  # 单文件上传
  [[userupload.types]]
    entryName = "test_upload"
    prefix = "/uploads"
    tmpPath = "./temp/uploads"
    maxSize = "500MB"
    storage = [
      { type = "local", path = "uploads/files" },
      { type = "s3", target = "garage-primary", bucket = "test-bucket" }
    ]

  # 分块上传
  [[userupload.types]]
    entryName = "chunked_upload"
    prefix = "/chunked"
    tmpPath = "./temp/chunked"
    maxSize = "10GB"
    storage = [
      { type = "local", path = "uploads/chunked" },
      { type = "s3", target = "garage-primary", bucket = "test-bucket" }
    ]
```

## 🧪 测试场景

### 单文件上传测试
1. 选择"单文件上传"模式
2. 选择一个小于500MB的文件
3. 输入用户ID（可选）
4. 点击"开始上传"
5. 观察上传进度和结果

### 目录上传测试
1. 准备测试目录和文件
2. 使用curl命令测试目录上传
3. 验证文件结构和内容

```bash
# 创建测试目录
mkdir -p test_dir/subdir
echo "Hello file1" > test_dir/file1.txt
echo "Hello file2" > test_dir/subdir/file2.txt

# 目录上传
curl -X POST http://localhost:8080/api/directory/upload \
  -F "uid=test_user" \
  -F "directory_name=my_test_directory" \
  -F "max_size=1048576" \
  -F "files=@test_dir/file1.txt;filename=file1.txt" \
  -F "files=@test_dir/subdir/file2.txt;filename=subdir/file2.txt"
```

### 分块上传测试
1. 选择"分块上传"模式
2. 选择一个大文件
3. 配置分块大小
4. 输入用户ID（可选）
5. 点击"开始分块上传"
6. 观察分块进度和速度
7. 可以测试中止功能

## 📁 目录结构

```plaintext
testserver/
├── main.go              # 服务器主程序
├── config.toml          # 配置文件
├── start-server.sh      # 启动脚本
├── get-garage-keys.sh   # 获取GarageHQ密钥脚本
├── static/              # 静态文件
│   ├── index.html       # 主页面
│   ├── app.js           # 前端JavaScript
│   └── style.css        # 样式文件
├── uploads/             # 上传文件存储
├── temp/                # 临时文件
└── logs/                # 日志文件
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   - 修改 config.toml 中的端口号
   - 或设置环境变量 `PORT=8081`

2. **配置文件未找到**
   - 确保在 testserver 目录中运行
   - 检查 config.toml 文件是否存在

3. **上传失败**
   - 检查文件大小限制
   - 确认存储目录权限
   - 查看服务器日志

### 日志查看

```bash
# 查看服务器日志
tail -f logs/goupload.log
```

## 🔗 相关链接

- [goupload 主项目](../README.md)
- [API 文档](../api.md)
