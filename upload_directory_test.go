package goupload

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"

	levelStore "github.com/real-rm/golevelstore"
	"github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestUploadDirectory_Basic 测试基本的目录上传功能
func TestUploadDirectory_Basic(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "documents", statsColl)
	if err != nil {
		t.<PERSON>("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 准备测试文件
	files := []DirectoryFileEntry{
		{
			RelativePath: "doc1.txt",
			Reader:       strings.NewReader("Hello, World! This is document 1."),
			Size:         33,
		},
		{
			RelativePath: "subdir/doc2.txt",
			Reader:       strings.NewReader("Hello, World! This is document 2 in subdirectory."),
			Size:         49,
		},
		{
			RelativePath: "subdir/nested/doc3.txt",
			Reader:       strings.NewReader("Document 3 in nested directory."),
			Size:         31,
		},
	}

	request := &DirectoryUploadRequest{
		Site:          "TEST",
		EntryName:     "documents",
		UserID:        "user123",
		DirectoryName: "test_documents",
		Files:         files,
		MaxSize:       1024 * 1024, // 1MB
	}

	// 执行上传
	result, err := UploadDirectory(context.Background(), statsUpdater, request)

	// 验证结果
	assert.NoError(t, err, "目录上传应该成功")
	assert.NotNil(t, result, "结果不应该为nil")
	assert.Equal(t, 3, result.TotalFiles, "应该有3个文件")
	assert.Equal(t, int64(113), result.TotalSize, "总大小应该是113字节")
	assert.Len(t, result.SuccessFiles, 3, "应该有3个成功上传的文件")
	assert.Len(t, result.FailedFiles, 0, "不应该有失败的文件")
	assert.False(t, result.IsPartialUpload, "不应该是部分上传")
	assert.NotEmpty(t, result.DirectoryPath, "目录路径不应该为空")

	// 验证文件路径
	expectedFiles := []string{"doc1.txt", "subdir/doc2.txt", "subdir/nested/doc3.txt"}
	assert.ElementsMatch(t, expectedFiles, result.SuccessFiles, "成功上传的文件列表应该匹配")

	// 验证写入路径
	assert.NotEmpty(t, result.WrittenPaths, "应该有写入路径")
}

// TestUploadDirectory_Validation 测试请求验证
func TestUploadDirectory_Validation(t *testing.T) {
	ctx := context.Background()
	mockStats := &mockStatsUpdater{}

	tests := []struct {
		name    string
		request *DirectoryUploadRequest
		wantErr string
	}{
		{
			name:    "nil request",
			request: nil,
			wantErr: "request cannot be nil",
		},
		{
			name: "empty site",
			request: &DirectoryUploadRequest{
				Site:          "",
				EntryName:     "documents",
				UserID:        "user123",
				DirectoryName: "test",
				Files:         []DirectoryFileEntry{{RelativePath: "test.txt", Reader: strings.NewReader("test")}},
			},
			wantErr: "site cannot be empty",
		},
		{
			name: "empty entryName",
			request: &DirectoryUploadRequest{
				Site:          "TEST",
				EntryName:     "",
				UserID:        "user123",
				DirectoryName: "test",
				Files:         []DirectoryFileEntry{{RelativePath: "test.txt", Reader: strings.NewReader("test")}},
			},
			wantErr: "entryName cannot be empty",
		},
		{
			name: "empty userID",
			request: &DirectoryUploadRequest{
				Site:          "TEST",
				EntryName:     "documents",
				UserID:        "",
				DirectoryName: "test",
				Files:         []DirectoryFileEntry{{RelativePath: "test.txt", Reader: strings.NewReader("test")}},
			},
			wantErr: "userID cannot be empty",
		},
		{
			name: "empty directoryName",
			request: &DirectoryUploadRequest{
				Site:          "TEST",
				EntryName:     "documents",
				UserID:        "user123",
				DirectoryName: "",
				Files:         []DirectoryFileEntry{{RelativePath: "test.txt", Reader: strings.NewReader("test")}},
			},
			wantErr: "directoryName cannot be empty",
		},
		{
			name: "empty files",
			request: &DirectoryUploadRequest{
				Site:          "TEST",
				EntryName:     "documents",
				UserID:        "user123",
				DirectoryName: "test",
				Files:         []DirectoryFileEntry{},
			},
			wantErr: "files list cannot be empty",
		},
		{
			name: "path traversal attack",
			request: &DirectoryUploadRequest{
				Site:          "TEST",
				EntryName:     "documents",
				UserID:        "user123",
				DirectoryName: "test",
				Files: []DirectoryFileEntry{
					{RelativePath: "../../../etc/passwd", Reader: strings.NewReader("test")},
				},
			},
			wantErr: "path traversal not allowed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := UploadDirectory(ctx, mockStats, tt.request)
			assert.Error(t, err, "应该返回错误")
			assert.Contains(t, err.Error(), tt.wantErr, "错误信息应该包含预期内容")
		})
	}
}

// TestUploadDirectory_SizeLimit 测试大小限制
func TestUploadDirectory_SizeLimit(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "documents", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 创建超过限制的文件
	largeContent := strings.Repeat("A", 1024) // 1KB
	files := []DirectoryFileEntry{
		{
			RelativePath: "large1.txt",
			Reader:       strings.NewReader(largeContent),
			Size:         1024,
		},
		{
			RelativePath: "large2.txt",
			Reader:       strings.NewReader(largeContent),
			Size:         1024,
		},
	}

	request := &DirectoryUploadRequest{
		Site:          "TEST",
		EntryName:     "documents",
		UserID:        "user123",
		DirectoryName: "large_test",
		Files:         files,
		MaxSize:       1500, // 1.5KB，应该超过限制
	}

	// 执行上传
	_, err = UploadDirectory(context.Background(), statsUpdater, request)

	// 应该返回大小超限错误
	assert.Error(t, err, "应该返回大小超限错误")
	assert.Contains(t, err.Error(), "exceeds limit", "错误信息应该包含超限信息")
}

// TestValidateRelativePath 测试路径验证
func TestValidateRelativePath(t *testing.T) {
	tests := []struct {
		name    string
		path    string
		wantErr bool
	}{
		{"valid path", "subdir/file.txt", false},
		{"valid nested path", "a/b/c/file.txt", false},
		{"path traversal", "../file.txt", true},
		{"path traversal nested", "subdir/../../../etc/passwd", true},
		{"absolute path", "/etc/passwd", true},
		{"windows absolute path", "C:\\Windows\\System32", true},
		{"too long path", strings.Repeat("a", 501), true},
		{"empty path", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateRelativePath(tt.path)
			if tt.wantErr {
				assert.Error(t, err, "应该返回错误")
			} else {
				assert.NoError(t, err, "不应该返回错误")
			}
		})
	}
}

// TestDetermineDirectoryLimit 测试目录大小限制确定逻辑
func TestDetermineDirectoryLimit(t *testing.T) {
	tests := []struct {
		name           string
		configMaxSize  string
		requestMaxSize int64
		expected       int64
	}{
		{
			name:           "use request limit when specified",
			configMaxSize:  "100MB",
			requestMaxSize: 50 * 1024 * 1024, // 50MB
			expected:       50 * 1024 * 1024,
		},
		{
			name:           "use config limit when request is 0",
			configMaxSize:  "200MB",
			requestMaxSize: 0,
			expected:       200 * 1024 * 1024,
		},
		{
			name:           "use default when config is empty",
			configMaxSize:  "",
			requestMaxSize: 0,
			expected:       1024 * 1024 * 1024, // 1GB default
		},
		{
			name:           "use default when config is invalid",
			configMaxSize:  "invalid",
			requestMaxSize: 0,
			expected:       1024 * 1024 * 1024, // 1GB default
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := determineDirectoryLimit(tt.configMaxSize, tt.requestMaxSize)
			assert.Equal(t, tt.expected, result, "目录大小限制应该正确")
		})
	}
}

// TestPreprocessDirectoryFiles 测试目录文件预处理
func TestPreprocessDirectoryFiles(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	t.Run("successful preprocessing", func(t *testing.T) {
		files := []DirectoryFileEntry{
			{
				RelativePath: "file1.txt",
				Reader:       strings.NewReader("content1"),
				Size:         8,
			},
			{
				RelativePath: "file2.txt",
				Reader:       strings.NewReader("content2"),
				Size:         8,
			},
		}

		processedFiles, totalSize, cleanup, err := preprocessDirectoryFiles(
			files,
			1024*1024, // 1MB limit
			"/tmp",
		)

		assert.NoError(t, err, "预处理应该成功")
		assert.Len(t, processedFiles, 2, "应该有2个处理后的文件")
		assert.Equal(t, int64(16), totalSize, "总大小应该是16字节")
		assert.NotNil(t, cleanup, "应该有清理函数")

		if cleanup != nil {
			cleanup()
		}
	})

	t.Run("size limit exceeded", func(t *testing.T) {
		files := []DirectoryFileEntry{
			{
				RelativePath: "large.txt",
				Reader:       strings.NewReader(strings.Repeat("A", 1024)),
				Size:         1024,
			},
		}

		_, _, cleanup, err := preprocessDirectoryFiles(
			files,
			512, // 512 bytes limit, smaller than file
			"/tmp",
		)

		assert.Error(t, err, "应该返回大小超限错误")
		assert.Contains(t, err.Error(), "exceeds limit", "错误信息应该包含超限信息")

		if cleanup != nil {
			cleanup()
		}
	})
}

// TestUploadDirectory_FullIntegration 测试完整的目录上传流程
func TestUploadDirectory_FullIntegration(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "documents", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 准备测试文件
	files := []DirectoryFileEntry{
		{
			RelativePath: "readme.txt",
			Reader:       strings.NewReader("This is a readme file."),
			Size:         22,
		},
		{
			RelativePath: "docs/guide.md",
			Reader:       strings.NewReader("# User Guide\n\nThis is a guide."),
			Size:         30,
		},
	}

	request := &DirectoryUploadRequest{
		Site:          "TEST",
		EntryName:     "documents",
		UserID:        "integration_user",
		DirectoryName: "integration_test",
		Files:         files,
		MaxSize:       1024 * 1024, // 1MB
	}

	// 执行上传
	result, err := UploadDirectory(context.Background(), statsUpdater, request)

	// 验证结果
	assert.NoError(t, err, "目录上传应该成功")
	assert.NotNil(t, result, "结果不应该为nil")
	assert.Equal(t, 2, result.TotalFiles, "应该有2个文件")
	assert.Equal(t, int64(52), result.TotalSize, "总大小应该是52字节")
	assert.Len(t, result.SuccessFiles, 2, "应该有2个成功上传的文件")
	assert.Len(t, result.FailedFiles, 0, "不应该有失败的文件")
	assert.False(t, result.IsPartialUpload, "不应该是部分上传")
}

// TestExecuteDirectoryUpload 测试目录上传执行逻辑
func TestExecuteDirectoryUpload(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	// 准备预处理后的文件
	tempDir, err := os.MkdirTemp("", "test_execute_dir_*")
	require.NoError(t, err, "创建临时目录应该成功")
	defer func() { _ = os.RemoveAll(tempDir) }()

	// 创建临时文件
	file1Path := filepath.Join(tempDir, "file1.txt")
	file2Path := filepath.Join(tempDir, "file2.txt")

	err = os.WriteFile(file1Path, []byte("content of file 1"), 0644)
	require.NoError(t, err, "创建文件1应该成功")

	err = os.WriteFile(file2Path, []byte("content of file 2"), 0644)
	require.NoError(t, err, "创建文件2应该成功")

	// 打开文件
	file1, err := os.Open(file1Path)
	require.NoError(t, err, "打开文件1应该成功")
	defer func() { _ = file1.Close() }()

	file2, err := os.Open(file2Path)
	require.NoError(t, err, "打开文件2应该成功")
	defer func() { _ = file2.Close() }()

	// 获取实际文件大小
	stat1, err := file1.Stat()
	require.NoError(t, err, "获取文件1信息应该成功")
	stat2, err := file2.Stat()
	require.NoError(t, err, "获取文件2信息应该成功")

	// 创建FileInfo
	fileInfo1 := &FileInfo{
		Size:     stat1.Size(),
		MimeType: "text/plain",
	}
	fileInfo2 := &FileInfo{
		Size:     stat2.Size(),
		MimeType: "text/plain",
	}

	processedFiles := []ProcessedDirectoryFile{
		{
			RelativePath: "docs/readme.txt",
			Reader:       file1,
			FileInfo:     fileInfo1,
		},
		{
			RelativePath: "docs/guide.txt",
			Reader:       file2,
			FileInfo:     fileInfo2,
		},
	}

	// 获取配置
	config, err := levelStore.GetUserUploadConfig("TEST", "documents", nil)
	if err != nil {
		t.Skipf("跳过测试：获取配置失败: %v", err)
	}

	// 获取S3配置
	s3ProviderMap, err := getS3ProviderMap(context.Background(), config.Storage)
	require.NoError(t, err, "获取S3配置应该成功")

	// 执行目录上传
	result, err := executeDirectoryUpload(
		context.Background(),
		config,
		s3ProviderMap,
		processedFiles,
		"test_directory",
		map[string]string{
			"user-id": "integration_user",
		},
	)

	// 验证结果
	assert.NoError(t, err, "目录上传执行应该成功")
	assert.NotNil(t, result, "结果不应该为nil")
	assert.Equal(t, 2, result.TotalFiles, "应该有2个文件")
	expectedTotalSize := stat1.Size() + stat2.Size()
	assert.Equal(t, expectedTotalSize, result.TotalSize, "总大小应该是两个文件大小之和")
	assert.Len(t, result.SuccessFiles, 2, "应该有2个成功文件")
	assert.Len(t, result.FailedFiles, 0, "不应该有失败文件")
	assert.False(t, result.IsPartialUpload, "不应该是部分上传")
}
